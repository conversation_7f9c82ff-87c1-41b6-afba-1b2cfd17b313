require("dotenv").config();

const { MongoClient } = require("mongodb");
const dayjs = require("dayjs");
const advancedFormat = require("dayjs/plugin/advancedFormat");
const utc = require('dayjs/plugin/utc');
const timezone = require('dayjs/plugin/timezone');

// Configure dayjs with plugins
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(advancedFormat);

// Global error handling
process.on('unhandledRejection', error => {
  console.error('Unhandled promise rejection:', error);
});

// Initialize MongoDB client with more robust connection options
const mongoClient = new MongoClient(process.env.MONGO_URI, {
  serverSelectionTimeoutMS: 30000, // 30 seconds
  socketTimeoutMS: 45000, // 45 seconds
  connectTimeoutMS: 30000, // 30 seconds
  maxPoolSize: 10, // Maintain up to 10 connections
  maxIdleTimeMS: 60000, // 1 minute - close idle connections after this time
  minPoolSize: 2 // Keep at least 2 connections 
});

// Function to check if MongoDB is connected and reconnect if necessary
async function ensureMongoConnection() {
  try {
    // Check if the client is still connected
    if (!mongoClient.topology || !mongoClient.topology.isConnected()) {
      console.log("MongoDB connection lost, reconnecting...");
      await mongoClient.connect();
      console.log("Successfully reconnected to MongoDB");
    }
    return true;
  } catch (error) {
    console.error("Error connecting to MongoDB:", error);
    return false;
  }
}

// Function to safely close MongoDB connection
async function safeMongoClose() {
  try {
    if (mongoClient && mongoClient.topology && mongoClient.topology.isConnected()) {
      await mongoClient.close();
      console.log("MongoDB connection closed safely");
    }
  } catch (error) {
    console.error("Error closing MongoDB connection:", error);
  }
}

module.exports = { mongoClient, dayjs, ensureMongoConnection, safeMongoClose };