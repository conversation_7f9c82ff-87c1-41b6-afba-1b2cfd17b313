#!/bin/bash

# DFS Feed Startup Script with Enhanced Monitoring
# This script provides additional safeguards for long-running deployments

echo "🚀 Starting DFS Feed with enhanced monitoring..."

# Set environment variables for optimal performance
export NODE_ENV=production
export PUPPETEER_DISABLE_HEADLESS_WARNING=true
export CHROME_DEVEL_SANDBOX=/usr/bin/google-chrome-stable

# Function to cleanup processes on exit
cleanup() {
    echo "🧹 Cleaning up processes..."
    pkill -f chrome 2>/dev/null || true
    pkill -f puppeteer 2>/dev/null || true
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Function to monitor and restart if needed
monitor_and_restart() {
    local max_restarts=5
    local restart_count=0
    local start_time=$(date +%s)
    
    while [ $restart_count -lt $max_restarts ]; do
        echo "📊 Starting application (attempt $((restart_count + 1))/$max_restarts)..."
        
        # Start the Node.js application with enhanced flags
        node --expose-gc \
             --max-old-space-size=4096 \
             --optimize-for-size \
             --gc-interval=100 \
             index.js &
        
        local app_pid=$!
        echo "🔄 Application started with PID: $app_pid"
        
        # Monitor the application
        while kill -0 $app_pid 2>/dev/null; do
            sleep 30
            
            # Check if we've been running for more than 24 hours
            local current_time=$(date +%s)
            local uptime=$((current_time - start_time))
            
            if [ $uptime -gt 86400 ]; then  # 24 hours in seconds
                echo "⏰ Application has been running for 24+ hours, performing restart..."
                kill -TERM $app_pid
                sleep 10
                kill -KILL $app_pid 2>/dev/null || true
                break
            fi
            
            # Check memory usage
            local memory_usage=$(free | grep Mem | awk '{printf "%.0f", $3/$2 * 100.0}')
            if [ "$memory_usage" -gt 90 ]; then
                echo "🚨 High memory usage detected ($memory_usage%), restarting application..."
                kill -TERM $app_pid
                sleep 10
                kill -KILL $app_pid 2>/dev/null || true
                break
            fi
        done
        
        # Wait for the process to exit
        wait $app_pid
        local exit_code=$?
        
        echo "📉 Application exited with code: $exit_code"
        
        # If exit code is 0, it was a graceful shutdown (likely intentional restart)
        if [ $exit_code -eq 0 ]; then
            echo "✅ Graceful shutdown detected, restarting..."
            restart_count=0  # Reset restart count for graceful shutdowns
        else
            restart_count=$((restart_count + 1))
            echo "❌ Unexpected exit, restart attempt $restart_count/$max_restarts"
            
            if [ $restart_count -lt $max_restarts ]; then
                echo "⏳ Waiting 30 seconds before restart..."
                sleep 30
            fi
        fi
        
        # Cleanup any lingering processes
        echo "🧹 Cleaning up lingering processes..."
        pkill -f chrome 2>/dev/null || true
        pkill -f puppeteer 2>/dev/null || true
        
        # Force garbage collection if possible
        sync
        echo 3 > /proc/sys/vm/drop_caches 2>/dev/null || true
        
        # Reset start time for new instance
        start_time=$(date +%s)
    done
    
    echo "💥 Maximum restart attempts reached. Exiting."
    exit 1
}

# Check if we're in a container environment
if [ -f /.dockerenv ]; then
    echo "🐳 Running in Docker container"
    
    # In container environments, let the orchestrator handle restarts
    echo "🎯 Starting application with container-optimized settings..."
    exec node --expose-gc \
              --max-old-space-size=4096 \
              --optimize-for-size \
              --gc-interval=100 \
              index.js
else
    echo "🖥️  Running on host system"
    # On host systems, use our monitoring and restart logic
    monitor_and_restart
fi
