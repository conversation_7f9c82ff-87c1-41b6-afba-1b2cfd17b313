# Use an official Node image with platform specified for ARM64 compatibility
FROM --platform=linux/amd64 node:18

# Install dependencies needed for Puppeteer and fonts
RUN apt-get update && apt-get install -y \
    fonts-liberation \
    gconf-service \
    libappindicator1 \
    libasound2 \
    libatk1.0-0 \
    libcairo2 \
    libcups2 \
    libfontconfig1 \
    libgbm-dev \
    libgdk-pixbuf2.0-0 \
    libgtk-3-0 \
    libicu-dev \
    libjpeg-dev \
    libnspr4 \
    libnss3 \
    libpango-1.0-0 \
    libpangocairo-1.0-0 \
    libpng-dev \
    libx11-6 \
    libx11-xcb1 \
    libxcb1 \
    libxcomposite1 \
    libxcursor1 \
    libxdamage1 \
    libxext6 \
    libxfixes3 \
    libxi6 \
    libxrandr2 \
    libxrender1 \
    libxss1 \
    libxtst6 \
    xdg-utils \
    wget \
    unzip \
    ca-certificates \
    fontconfig \
    fonts-noto \
    fonts-noto-cjk \
    && rm -rf /var/lib/apt/lists/*

# Create app directory
WORKDIR /usr/src/app

# Copy package.json/package-lock.json first (for caching)
COPY package*.json ./

# Set environment variables for Puppeteer
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
ENV PUPPETEER_EXECUTABLE_PATH=/usr/bin/google-chrome-stable
ENV CHROME_BIN=/usr/bin/google-chrome-stable
ENV PUPPETEER_DISABLE_HEADLESS_WARNING=true

# Install Chrome for Puppeteer with updated key handling
RUN wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | gpg --dearmor -o /usr/share/keyrings/googlechrome-linux-keyring.gpg \
    && echo "deb [arch=amd64 signed-by=/usr/share/keyrings/googlechrome-linux-keyring.gpg] http://dl.google.com/linux/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google.list \
    && apt-get update \
    && apt-get install -y google-chrome-stable \
    && rm -rf /var/lib/apt/lists/*

# Create a non-root user for running Chrome (security best practice)
RUN groupadd -r pptruser && useradd -r -g pptruser -G audio,video pptruser \
    && mkdir -p /home/<USER>/Downloads \
    && chown -R pptruser:pptruser /home/<USER>

# Set proper permissions for Chrome
RUN chmod 4755 /usr/bin/google-chrome-stable

# Install dependencies
RUN npm install

# Copy your entire project (including site/ and font files) to container
COPY . .

# Make startup script executable
RUN chmod +x start.sh

# Download and install Inter font
RUN mkdir -p /usr/local/share/fonts/inter \
    && wget -q -O inter.zip https://github.com/rsms/inter/releases/download/v3.19/Inter-3.19.zip \
    && unzip -q inter.zip -d /tmp/inter \
    && find /tmp/inter -name "*.otf" -exec cp {} /usr/local/share/fonts/inter/ \; \
    && rm -rf /tmp/inter inter.zip \
    && fc-cache -f -v

# Change ownership of the app directory to pptruser
RUN chown -R pptruser:pptruser /usr/src/app

# Switch to non-root user
USER pptruser

# Add health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD node healthcheck.js

# Expose any ports if your React app or Node server is running
# EXPOSE 3000

# Build your React site if needed (uncomment if your typical process is to do so)
# RUN npm run build

# Final command: Use our enhanced startup script
CMD ["./start.sh"]
