const { mongoClient, dayjs } = require('./config');
const { siteUrls, collectionImages, providerColors } = require('./config/sites');
const generateHtmlFromDocuments = require('./html/generateHtmlFromDocuments');
const generateHtmlAndScreenshot = require('./html/generateHtmlAndScreenshot');
const filterBets = require('./html/filterBets');
const betsTracker = require('./utils/betsTracker');
const memoryManager = require('./utils/memoryManager');
const fetch = require('node-fetch');
const FormData = require('form-data');
const fs = require('fs');

// MongoDB connection management functions
async function ensureMongoConnection() {
  try {
    if (!mongoClient.topology || !mongoClient.topology.isConnected()) {
      await mongoClient.connect();
      console.log("Reconnected to MongoDB.");
    }
    return true;
  } catch (error) {
    console.error("Failed to ensure MongoDB connection:", error);
    return false;
  }
}

async function safeMongoClose() {
  try {
    if (mongoClient.topology && mongoClient.topology.isConnected()) {
      await mongoClient.close();
      console.log("MongoDB connection closed.");
    }
  } catch (error) {
    console.error("Error closing MongoDB connection:", error);
  }
}

// Define the database name from environment variable, fallback to v2_prod if not set
const DATABASE_NAME = process.env.DB_NAME || "v2_prod";

// Function to fetch data without processing or sending updates
async function fetchDataOnly(collectionName) {
  try {
    // Ensure MongoDB connection is active
    const isConnected = await ensureMongoConnection();
    if (!isConnected) {
      throw new Error(`Failed to connect to MongoDB for ${collectionName}`);
    }

    console.log(`Fetching data for ${collectionName} (tracker initialization)...`);
    
    // Get the collection
    const collection = mongoClient.db(DATABASE_NAME).collection(collectionName);
    
    // Fetch documents
    let documents = await collection.find({}).toArray();
    
    // Filter for future events only
    const currentTime = dayjs();
    documents = documents.filter(doc => {
      const startTime = dayjs(doc.start_time);
      return startTime.isAfter(currentTime);
    });
    
    if (documents.length === 0) {
      console.log(`No future events found for ${collectionName}.`);
      return;
    }
    
    console.log(`Found ${documents.length} documents for ${collectionName} (tracker initialization).`);
    
    // Apply business logic filters
    const filteredDocuments = filterBets(documents);
    console.log(`After filtering, ${filteredDocuments.length} documents remain for ${collectionName} (tracker initialization).`);
    
    // If no documents pass the filter, exit early
    if (filteredDocuments.length === 0) {
      console.log(`No documents passed the filter criteria for ${collectionName}.`);
      return;
    }
    
    // Sort by highest EV
    filteredDocuments.sort((a, b) => {
      const evA = parseFloat(a.avg_ev_display.replace("%", "")) || 0;
      const evB = parseFloat(b.avg_ev_display.replace("%", "")) || 0;
      return evB - evA; // For descending order
    });
    
    // Just initialize the bets tracker with the data (don't check for changes or send updates)
    betsTracker.updateHighEvBets(collectionName, filteredDocuments);
    console.log(`Initialized bets tracker for ${collectionName} with ${filteredDocuments.length} bets.`);
    
  } catch (error) {
    console.error(`Error fetching data for ${collectionName}:`, error);
  }
}

// Function to fetch and process data for a specific collection
async function fetchAndProcessData(collectionName, forceSend = false) {
  try {
    // Ensure MongoDB connection is active
    const isConnected = await ensureMongoConnection();
    if (!isConnected) {
      throw new Error(`Failed to connect to MongoDB for ${collectionName}`);
    }

    console.log(`Fetching data for ${collectionName}...`);
    
    // Get the collection
    const collection = mongoClient.db(DATABASE_NAME).collection(collectionName);
    
    // Fetch documents
    let documents = await collection.find({}).toArray();
    
    // Filter for future events only
    const currentTime = dayjs();
    documents = documents.filter(doc => {
      const startTime = dayjs(doc.start_time);
      return startTime.isAfter(currentTime);
    });
    
    if (documents.length === 0) {
      console.log(`No future events found for ${collectionName}.`);
      return;
    }
    
    console.log(`Found ${documents.length} documents for ${collectionName}.`);
    
    // Apply business logic filters
    const filteredDocuments = filterBets(documents);
    console.log(`After filtering, ${filteredDocuments.length} documents remain for ${collectionName}.`);
    
    // If no documents pass the filter, exit early
    if (filteredDocuments.length === 0) {
      console.log(`No documents passed the filter criteria for ${collectionName}.`);
      return;
    }
    
    // Sort by highest EV
    filteredDocuments.sort((a, b) => {
      const evA = parseFloat(a.avg_ev_display.replace("%", "")) || 0;
      const evB = parseFloat(b.avg_ev_display.replace("%", "")) || 0;
      return evB - evA; // For descending order
    });
    
    // Generate the PrizePicks slip link URL based on the top 6 documents by EV%
    let slipLinkUrl = null;
    if (collectionName === 'PrizePicks') {
      const topDocuments = filteredDocuments.slice(0, 6);
      const projStrings = topDocuments.map(doc => doc.proj_string).filter(Boolean);
      if (projStrings.length > 0) {
        const projectionsParam = projStrings.join(',');
        slipLinkUrl = `https://app.prizepicks.com/?projections=${projectionsParam}`;
      }
    }
    
    // First, update the bets tracker with the new data
    const hasChanges = betsTracker.updateHighEvBets(collectionName, filteredDocuments);
    
    // Determine if we should send updates based on changes or time interval
    const shouldSendDueToTime = betsTracker.shouldSendUpdates(collectionName, forceSend);
    const shouldSend = hasChanges || shouldSendDueToTime;
    
    if (!shouldSend) {
      console.log(`No significant changes detected for ${collectionName} and time interval not reached, skipping update.`);
      return;
    }
    
    if (shouldSendDueToTime && !hasChanges) {
      console.log(`No changes detected for ${collectionName}, but sending update due to time interval.`);
    } else if (hasChanges) {
      console.log(`Changes detected for ${collectionName}, sending update.`);
    }
    
    // Determine which webhook URL to use
    let webhookUrl;
    if (collectionName === 'PrizePicks') {
      webhookUrl = process.env.PRIZEPICKS_WEBHOOK_URL;
    } else if (collectionName === 'Underdog') {
      webhookUrl = process.env.UNDERDOG_WEBHOOK_URL;
    } else if (collectionName === 'Betr') {
      webhookUrl = process.env.BETR_WEBHOOK_URL;
    } else if (collectionName === 'Dabble') {
      webhookUrl = process.env.DABBLE_WEBHOOK_URL;
    } else if (collectionName === 'Sleeper') {
      webhookUrl = process.env.SLEEPER_WEBHOOK_URL;
    } else if (collectionName === 'VividPicks') { // Add VividPicks
      webhookUrl = process.env.VIVIDPICKS_WEBHOOK_URL;
    } else if (collectionName === 'Hotstreak') { // Add Hotstreak
      webhookUrl = process.env.HOTSTREAK_WEBHOOK_URL;
    } else if (collectionName === 'OwnersBox') { // Add OwnersBox
      webhookUrl = process.env.OWNERSBOX_WEBHOOK_URL;
    } else {
      console.error(`No webhook URL configured for ${collectionName}`);
      return;
    }
    
    // Process each page individually using the original filtered documents
    await processDocumentsInPages(filteredDocuments, webhookUrl, collectionName, slipLinkUrl);
    
    // Mark as sent
    betsTracker.markAsSent(collectionName);
    
  } catch (error) {
    console.error(`Error processing data for ${collectionName}:`, error);
    // Don't throw the error so the interval continues
  }
}

// Function to process documents in pages, generating HTML, taking screenshots, and sending to Discord
async function processDocumentsInPages(documents, webhookUrl, collectionName, slipLinkUrl = null, itemsPerPage = 10) {
  // Get the first page to determine total pages
  const firstPageResult = generateHtmlFromDocuments(documents, null, itemsPerPage, 1);
  const { totalPages } = firstPageResult;
  
  console.log(`Processing ${totalPages} pages for ${collectionName}...`);
  
  // Process each page one by one
  for (let pageNumber = 1; pageNumber <= totalPages; pageNumber++) {
    try {
      console.log(`Generating page ${pageNumber} of ${totalPages} for ${collectionName}...`);
      
      // Generate HTML for the current page
      const pageResult = generateHtmlFromDocuments(documents, null, itemsPerPage, pageNumber);
      
      // Take screenshot of the current page
      const screenshotBuffer = await generateHtmlAndScreenshot(pageResult.html);
      
      // Send the current page to Discord
      await sendToDiscordWebhook(webhookUrl, screenshotBuffer, collectionName, pageNumber, totalPages, slipLinkUrl);
    } catch (error) {
      console.error(`Error processing data for ${collectionName}: ${error.message}`);
      // Continue with next page if there's an error
      continue;
    }
  }
  
  console.log(`Completed processing all ${totalPages} pages for ${collectionName}.`);
}

// Function to send screenshot to Discord webhook
async function sendToDiscordWebhook(webhookUrl, screenshotBuffer, collectionName, pageNumber = 1, totalPages = 1, slipLinkUrl = null) {
  try {
    console.log(`Sending ${collectionName} page ${pageNumber}/${totalPages} data to Discord webhook...`);
    
    // Create a temporary file to store the screenshot
    const tempFilePath = `./${collectionName}_screenshot_page${pageNumber}.png`;
    fs.writeFileSync(tempFilePath, screenshotBuffer);
    
    // Create form data with the screenshot
    const formData = new FormData();
    formData.append('file', fs.createReadStream(tempFilePath), {
      filename: `${collectionName}_page${pageNumber}.png`,
      contentType: 'image/png'
    });
    
    // Construct the message content without the slip link
    let message = `${collectionName} Props `;
    if (totalPages > 1) {
      message += ` (Page ${pageNumber} of ${totalPages})`;
    }
    
    formData.append('content', message);
    
    // Send to webhook
    const response = await fetch(webhookUrl, {
      method: 'POST',
      body: formData
    });
    
    // Clean up the temporary file
    fs.unlinkSync(tempFilePath);
    
    if (!response.ok) {
      throw new Error(`Discord webhook returned ${response.status}: ${response.statusText}`);
    }
    
    console.log(`Successfully sent ${collectionName} page ${pageNumber}/${totalPages} data to Discord webhook.`);
    
    // Send the slip link as a follow-up message if it exists and this is the last page
    if (slipLinkUrl && pageNumber === totalPages) {
      // Small delay before sending the follow-up message
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Send the slip link as a follow-up message
      const linkResponse = await fetch(webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          content: `[PrizePicks Slip Link](${slipLinkUrl})`
        })
      });
      
      if (!linkResponse.ok) {
        throw new Error(`Discord webhook returned ${linkResponse.status}: ${linkResponse.statusText} when sending slip link`);
      }
      
      console.log(`Successfully sent PrizePicks slip link to Discord webhook.`);
    }
    
    // Add a small delay between webhook calls to avoid rate limiting
    if (pageNumber < totalPages) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  } catch (error) {
    console.error(`Error sending ${collectionName} page ${pageNumber}/${totalPages} data to Discord webhook:`, error);
    
    // Make sure to clean up the temporary file if it exists
    try {
      const tempFilePath = `./${collectionName}_screenshot_page${pageNumber}.png`;
      if (fs.existsSync(tempFilePath)) {
        fs.unlinkSync(tempFilePath);
      }
    } catch (cleanupError) {
      console.error('Error cleaning up temporary file:', cleanupError);
    }
  }
}

// Function to generate and send data for all collections
async function generateAndSendData(forceSend = false) {
  console.log('Starting data generation and sending process...');
  
  // Clean up old bets
  betsTracker.clearOldBets();
  
  // Process PrizePicks data
  await fetchAndProcessData('PrizePicks', forceSend);
  
  // Process Underdog data
  await fetchAndProcessData('Underdog', forceSend);
  
  // Process Betr data
  await fetchAndProcessData('Betr', forceSend);
  
  // Process Dabble data
  await fetchAndProcessData('Dabble', forceSend);
  
  // Process Sleeper data
  await fetchAndProcessData('Sleeper', forceSend);
  
  // Process VividPicks data
  await fetchAndProcessData('VividPicks', forceSend);
  
  // Process Hotstreak data
  await fetchAndProcessData('Hotstreak', forceSend);
  
  // Process OwnersBox data
  await fetchAndProcessData('OwnersBox', forceSend);

  console.log('Completed data generation and sending process.');
}

// Function to initialize MongoDB connection and start interval
async function initializeBot() {
  try {
    // Initial connection to MongoDB
    await mongoClient.connect();
    console.log("Connected to MongoDB.");
    
    // Initialize the bets tracker
    betsTracker.init();
    
    // Start memory monitoring
    memoryManager.startMonitoring();
    console.log("Memory monitoring started.");
    
    // Set up a periodic connection check (every 15 minutes)
    setInterval(async () => {
      await ensureMongoConnection();
    }, 15 * 60 * 1000);
    
    // Perform an initial data fetch just to populate the tracker (but don't send updates yet)
    console.log("Performing initial data fetch to populate bets tracker...");
    await fetchDataOnly('PrizePicks');
    await fetchDataOnly('Underdog');
    await fetchDataOnly('Betr');
    await fetchDataOnly('Dabble');
    await fetchDataOnly('Sleeper');
    // Add calls for new sources
    await fetchDataOnly('VividPicks');
    await fetchDataOnly('Hotstreak');
    await fetchDataOnly('OwnersBox');
    console.log("Initial data fetch complete - bets tracker populated.");
    
    // Short delay to ensure everything is initialized
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Run the data generation and sending process with force send
    await generateAndSendData(true);
    
    // Set up the interval for data generation and sending (every 15 seconds)
    // This will check for changes but only send updates if there are changes or the time interval has passed
    setInterval(async () => {
      await generateAndSendData(false);
    }, 15 * 1000); // 15 seconds
    
    console.log("Automated data generation and sending process started.");
    
  } catch (error) {
    console.error("Error initializing MongoDB connection:", error);
    process.exit(1);
  }
}

// Add proper shutdown handlers
process.on('SIGINT', async () => {
  console.log('Shutting down gracefully...');
  memoryManager.stopMonitoring();
  await safeMongoClose();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('Shutting down gracefully...');
  memoryManager.stopMonitoring();
  await safeMongoClose();
  process.exit(0);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught exception:', error);
  memoryManager.checkMemoryUsage();
});

// Call the initialize function to start the bot
initializeBot();