
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>DataWise Optimizer</title>
        <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
        <style>
            :root {
                --accent-color: #13161c;
                --base-font-size: 1.4rem; /* Further increased base font size for better readability */
                --primary-text: #ffffff; /* Primary text color for important data */
                --secondary-text: #a0aec0; /* Secondary text color for less important data */
                --highlight-color: #ffffff; /* Changed from green to white */
                --row-alt-color: #1a1e25; /* Alternating row color */
                --row-base-color: #13161c; /* Base row color */
            }
            body {
                font-size: var(--base-font-size);
                line-height: 1.6;
                background-color: var(--accent-color);
            }
            .site-image {
                width: 60px;
                height: 60px;
                border-radius: 50%;
                object-fit: cover;
                display: block;
                margin: 0 auto;
            }
            .pick-line-box {
              display: inline-block;
              background-color: #2d3748;
              border: 1px solid #4a5568;
              border-radius: 6px; /* Increased border radius */
              padding: 6px 10px; /* Increased padding */
              font-weight: 600; /* Made bolder for emphasis */
              min-width: 120px; /* Minimum width */
              color: white;
              font-size: 1.25rem; /* Further increased font size */
              box-shadow: 0 2px 4px rgba(0,0,0,0.2); /* Added subtle shadow */
              margin: 0 auto; /* Center the box */
              white-space: nowrap; /* Prevent line breaks */
              text-align: center; /* Center text */
          }
          /* Multiplier styling */
          .multiplier {
              display: inline-block;
              margin-left: 2px;
              color: white; /* Same color as main text */
              font-weight: 600; /* Match the font weight */
              font-size: 1.25rem; /* Match the font size */
          }
            .line {
                font-weight: normal;
                margin-left: 4px;
                margin-right: 4px;
            flex: 1;
            text-align: center;
            }
            .odds-box {
                display: inline-flex;
                justify-content: center;
                align-items: center;
                white-space: nowrap;
                background-color: #2d3748;
                --tw-border-opacity: 1;
                border-color: rgb(41 44 51 / var(--tw-border-opacity));
                border-radius: 4px;
                padding: 4px 6px;
                margin-left: 2px;
                color: white;
                font-size: 1.2rem; /* Increased font size */
                min-width: 85px; /* Slightly increased width */
                max-width: 95px; /* Slightly increased width */
                overflow: visible;
            }
            .odds-box-bet {
              display: flex; /* Adjusted to match flex display */
              justify-content: center;
              align-items: center;
              white-space: nowrap;
              background-color: #2d3748; /* Matches pick-line-box */
              border: 1px solid #4a5568; /* Matches pick-line-box */
              border-radius: 6px; /* Increased border radius */
              padding: 6px 10px; /* Increased padding */
              color: white; /* Matches pick-line-box */
              font-size: 1.25rem; /* Further increased font size */
              font-weight: 600; /* Made bolder for emphasis */
              min-width: 90px; /* Further increased for better readability */
              max-width: 110px; /* Further increased for better readability */
              overflow: visible; /* Specific to odds-box-bet */
              box-shadow: 0 2px 4px rgba(0,0,0,0.2); /* Added subtle shadow */
            }
            
            
            .custom-bg { background-color: var(--accent-color); }
            .custom-header { background-color: var(--accent-color); color: #f7fafc; }
            .custom-table-header { background-color: var(--accent-color); }
            .custom-table-header img {
                height: 32px;
                width: auto;
            }
            .custom-table-row { background-color: #2d3748; }
            .data-container {
                max-width: 100vw;
                margin: auto;
                box-shadow: 0 4px 6px rgba(0,0,0,0.3); /* Increased shadow depth */
                border-radius: 0.75rem; /* Increased border radius */
                overflow: visible;
            }
            .wide-column {
                width: 120px;
            }
            
            .narrow-column {
                width: 100px;
            }
            .progress-segment {
                height: 2px;
                width: 20px;
                clip-path: polygon(40% 0px, 100% 0px, 60% 100%, 0px 100%);
              }
            table {
                table-layout: fixed;
                min-width: 1100px;
                width: 100%;
                border-collapse: separate;
                border-spacing: 0 1px; /* Add slight spacing between rows */
            }
            .custom-table-row {
                height: 68px; /* Further increased row height */
            }
            
            .custom-table-row td, .custom-table-row th {
                font-size: 1.4rem; /* Further increased font size for better readability */
                padding: 10px; /* Added padding for better spacing */
            }
            /* Alternating row colors with increased contrast */
            tr:nth-child(odd) {
                background-color: var(--row-base-color);
            }
            tr:nth-child(even) {
                background-color: var(--row-alt-color);
            }
            /* Add hover effect for better visual feedback */
            tr:hover {
                background-color: #2d3748;
            }
            /* Add styling for important values */
            .ev-value, .win-value {
                font-weight: 700;
                font-size: 1.4rem; /* Further increased font size */
                color: var(--primary-text);
            }
            /* Style for high EV values - no longer using green text */
            .high-ev {
                color: var(--primary-text) !important;
                font-weight: 700; /* Using bold instead of color to emphasize */
            }
            /* Specifically target the player data cell for more width */
            th.player-data, td.player-data {
                width: 180px;
                text-align: center;
            }
        
            /* For columns that can afford to be narrower, adjust accordingly */
            th.odds-column, td.odds-column {
                width: 80px;
                text-align: center;
            }
            
            /* Ensure all table cells are centered */
            table th, table td {
                text-align: center;
            }
            .odds-div{
                display: flex;
                justify-content: center;
                align-items: center;
            }
            .thin{
                width: 100px;
            }
            .td-container {
                display: flex;
                justify-content: center;
                align-items: center;
                height: 100%; /* Ensure both elements fill the height of the td */
                gap: 2px; /* Reduced gap between elements */
            }
            .wide{
                width: 220px; /* Reduced from 290px */
            }
            /* Pagination styles */
            .pagination-container {
                display: flex;
                justify-content: center;
                align-items: center;
                margin: 20px 0;
                color: white;
                font-size: 1.2rem;
            }
            .pagination-info {
                margin: 0 15px;
                font-weight: 600;
            }
            .pagination-button {
                background-color: #2d3748;
                border: 1px solid #4a5568;
                border-radius: 6px;
                padding: 8px 16px;
                color: white;
                font-weight: 600;
                cursor: pointer;
                transition: background-color 0.2s;
                margin: 0 5px;
            }
            .pagination-button:hover {
                background-color: #4a5568;
            }
            .pagination-button:disabled {
                background-color: #1a202c;
                cursor: not-allowed;
                opacity: 0.5;
            
            }
            .less-wide{
              width: 215px;
          }
            /* Player name styling */
            .player-name {
                font-size: 1.4rem;
                font-weight: 700;
                color: #ffffff;
                margin-bottom: 4px;
            }
            /* Game details styling */
            .game-details {
                font-size: 0.85rem;
                color: #a0aec0;
                line-height: 1.3;
            }
            
        </style>
    </head>
    <body class="custom-bg text-gray-300 flex flex-col items-center justify-center">
<div class="data-container max-w-4xl w-full my-5">
    <div class="custom-header flex justify-center items-center px-6 py-6">
        <img src="https://github.com/tvnner2/sportsbook-images/blob/main/assets/Both_WhiteText_TransparentBackground.png?raw=true" alt="DataWise Optimizer Logo" style="height: 40px; width: auto;">
    </div>
    <div class="pagination-container">
        <div class="pagination-info">Page 2 of 3</div>
    </div>
        <table class="min-w-full leading-normal">
                <thead>
                <tr class="custom-table-header text-gray-400" style="border-bottom: 2px solid #2d3748;">
                <th class="thin text-center" style="padding: 1rem 1rem; font-size: 22px; font-weight: 700; color: #ffffff; letter-spacing: 0.05em; text-transform: uppercase; border-bottom: 2px solid #1e293b;">Site</th>
                <th class="less-wide text-center" style="padding: 1rem 1rem; font-size: 22px; font-weight: 700; color: #ffffff; letter-spacing: 0.05em; text-transform: uppercase; border-bottom: 2px solid #1e293b;">Player Info</th>
                <th class="wide text-center" style="padding: 1rem 1rem; font-size: 22px; font-weight: 700; color: #ffffff; letter-spacing: 0.05em; text-transform: uppercase; border-bottom: 2px solid #1e293b;">Bet Details</th>
                <th style="padding: 1rem 1rem; text-align: center; font-size: 22px; font-weight: 700; color: #ffffff; letter-spacing: 0.05em; text-transform: uppercase; border-bottom: 2px solid #1e293b;">EV %</th>
                <th style="padding: 1rem 1rem; text-align: center; font-size: 22px; font-weight: 700; color: #ffffff; letter-spacing: 0.05em; text-transform: uppercase; border-bottom: 2px solid #1e293b;">Fair Odds</th>
                        <!-- Dynamic provider columns -->
                        
    <th style="padding: 1rem 1rem; font-size: 22px; font-weight: 700; color: #ffffff; letter-spacing: 0.05em; text-transform: uppercase; border-bottom: 2px solid #1e293b;">
      <div class="flex justify-center">
          <img class="block mx-auto rounded-md" src="https://github.com/tvnner2/sportsbook-images/blob/main/assets/BetOnline.png?raw=true" alt="BetOnline" style="width: auto; height: 36px; border-radius: 6px;" />
      </div>
    </th>
  
    <th style="padding: 1rem 1rem; font-size: 22px; font-weight: 700; color: #ffffff; letter-spacing: 0.05em; text-transform: uppercase; border-bottom: 2px solid #1e293b;">
      <div class="flex justify-center">
          <img class="block mx-auto rounded-md" src="https://github.com/tvnner2/sportsbook-images/blob/main/assets/Kambi.png?raw=true" alt="BetRivers" style="width: auto; height: 36px; border-radius: 6px;" />
      </div>
    </th>
  
    <th style="padding: 1rem 1rem; font-size: 22px; font-weight: 700; color: #ffffff; letter-spacing: 0.05em; text-transform: uppercase; border-bottom: 2px solid #1e293b;">
      <div class="flex justify-center">
          <img class="block mx-auto rounded-md" src="https://github.com/tvnner2/sportsbook-images/blob/main/assets/Fanduel.png?raw=true" alt="Fanduel" style="width: auto; height: 36px; border-radius: 6px;" />
      </div>
    </th>
  
    <th style="padding: 1rem 1rem; font-size: 22px; font-weight: 700; color: #ffffff; letter-spacing: 0.05em; text-transform: uppercase; border-bottom: 2px solid #1e293b;">
      <div class="flex justify-center">
          <img class="block mx-auto rounded-md" src="https://github.com/tvnner2/sportsbook-images/blob/main/assets/DraftKings.png?raw=true" alt="DraftKings" style="width: auto; height: 36px; border-radius: 6px;" />
      </div>
    </th>
  
    <th style="padding: 1rem 1rem; font-size: 22px; font-weight: 700; color: #ffffff; letter-spacing: 0.05em; text-transform: uppercase; border-bottom: 2px solid #1e293b;">
      <div class="flex justify-center">
          <img class="block mx-auto rounded-md" src="https://github.com/tvnner2/sportsbook-images/blob/main/assets/Caesars.png?raw=true" alt="Caesars" style="width: auto; height: 36px; border-radius: 6px;" />
      </div>
    </th>
  
    <th style="padding: 1rem 1rem; font-size: 22px; font-weight: 700; color: #ffffff; letter-spacing: 0.05em; text-transform: uppercase; border-bottom: 2px solid #1e293b;">
      <div class="flex justify-center">
          <img class="block mx-auto rounded-md" src="https://github.com/tvnner2/sportsbook-images/blob/main/assets/pinnacle.png?raw=true" alt="Pinnacle" style="width: auto; height: 36px; border-radius: 6px;" />
      </div>
    </th>
  
                    </tr>
                </thead>
                <tbody>
                    
        <tr class="hover:bg-[#2d3748] transition-colors duration-150" style="background-color: #13161c; font-size: 1.4rem;">
            <td class="px-4 py-3 text-center"><img src="https://github.com/tvnner2/sportsbook-images/blob/main/assets/Underdog.png?raw=true" alt="Underdog" class="site-image" style="width: auto; height: 36px; border-radius: 6px;"></td>
            <td class="px-4 py-3 text-center">
              <div class="player-name" style="font-size: 1.5rem; font-weight: 600;">Devin Vassell</div>
              <div class="game-details" style="font-size: 0.95rem; color: #a0aec0;">NBA1Q &#8226; SAS vs. SAC<br>Fri, Mar 7, 10:00 PM EST</div>
            </td>
            <td class="px-4 py-3 text-center">
      <div class="grid grid-cols-1 items-center gap-y-1 justify-items-center">
        <div class="pick-line-box" title="Under 4.5 (1.10x)">
          <span style="display: inline-block;">Under 4.5</span><span class="multiplier"> (1.10x)</span>
        </div>
        <p style="color: #ffffff; font-size: 1.25rem; text-align: center; margin: 0; font-weight: 500;" class="text-[1.25rem] text-white">
          Points
        </p>
      </div>
        </td>
            <td class="px-4 py-3 high-ev">
      <div class="text-center w-full">
          <div class="ev-value font-bold mb-1 text-center" style="font-size: 1.5rem; color: #ffffff;">2.30%</div>
          <div class="flex justify-center gap-[2px]" aria-label="Expected value progress bar" role="progressbar" aria-valuenow="2.3" aria-valuemin="0" aria-valuemax="10">
              <div class="progress-segment" style="background-color: #ff3131; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #ff3131; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div>
          </div>
      </div>
      </td>
            <td class="px-4 py-3 odds-row"><div class="odds-div"><div class="border-[#292C33] odds-box-bet"><div class="odds-div"><strong>+100</strong></div></div></div></td>
            <td class="px-4 py-3"></td><td class="px-4 py-3"></td><td class="px-4 py-3"><div class="td-container flex items-center justify-center"><div class="mr-1">4.5</div><div class="odds-box" style="background-color:#0178ff; min-width: 100px; max-width: 110px;"><strong>-114</strong>&nbsp;/&nbsp;<strong>-114</strong></div></div></td><td class="px-4 py-3"></td><td class="px-4 py-3"></td><td class="px-4 py-3"></td>
        </tr>
        
        <tr class="hover:bg-[#2d3748] transition-colors duration-150" style="background-color: #1a1e25; font-size: 1.4rem;">
            <td class="px-4 py-3 text-center"><img src="https://github.com/tvnner2/sportsbook-images/blob/main/assets/Underdog.png?raw=true" alt="Underdog" class="site-image" style="width: auto; height: 36px; border-radius: 6px;"></td>
            <td class="px-4 py-3 text-center">
              <div class="player-name" style="font-size: 1.5rem; font-weight: 600;">Ollie Watkins</div>
              <div class="game-details" style="font-size: 0.95rem; color: #a0aec0;">SOCCER &#8226; Brentford vs Aston Villa<br>Sat, Mar 8, 12:30 PM EST</div>
            </td>
            <td class="px-4 py-3 text-center">
      <div class="grid grid-cols-1 items-center gap-y-1 justify-items-center">
        <div class="pick-line-box" title="Under 2.5 (1.13x)">
          <span style="display: inline-block;">Under 2.5</span><span class="multiplier"> (1.13x)</span>
        </div>
        <p style="color: #ffffff; font-size: 1.25rem; text-align: center; margin: 0; font-weight: 500;" class="text-[1.25rem] text-white">
          Shots
        </p>
      </div>
        </td>
            <td class="px-4 py-3 high-ev">
      <div class="text-center w-full">
          <div class="ev-value font-bold mb-1 text-center" style="font-size: 1.5rem; color: #ffffff;">2.30%</div>
          <div class="flex justify-center gap-[2px]" aria-label="Expected value progress bar" role="progressbar" aria-valuenow="2.3" aria-valuemin="0" aria-valuemax="10">
              <div class="progress-segment" style="background-color: #ff3131; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #ff3131; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div>
          </div>
      </div>
      </td>
            <td class="px-4 py-3 odds-row"><div class="odds-div"><div class="border-[#292C33] odds-box-bet"><div class="odds-div"><strong>+116</strong></div></div></div></td>
            <td class="px-4 py-3"><div class="td-container flex items-center justify-center"><div class="mr-1">2.5</div><div class="odds-box" style="background-color:#ec3538; min-width: 100px; max-width: 110px;"><strong>-120</strong>&nbsp;/&nbsp;<strong>-108</strong></div></div></td><td class="px-4 py-3"><div class="td-container flex items-center justify-center"><div class="mr-1">2.5</div><div class="odds-box" style="background-color:#013a62; min-width: 100px; max-width: 110px;"><strong>107</strong>&nbsp;/&nbsp;<strong>-186</strong></div></div></td><td class="px-4 py-3"></td><td class="px-4 py-3"></td><td class="px-4 py-3"></td><td class="px-4 py-3"></td>
        </tr>
        
        <tr class="hover:bg-[#2d3748] transition-colors duration-150" style="background-color: #13161c; font-size: 1.4rem;">
            <td class="px-4 py-3 text-center"><img src="https://github.com/tvnner2/sportsbook-images/blob/main/assets/Underdog.png?raw=true" alt="Underdog" class="site-image" style="width: auto; height: 36px; border-radius: 6px;"></td>
            <td class="px-4 py-3 text-center">
              <div class="player-name" style="font-size: 1.5rem; font-weight: 600;">Devin Vassell</div>
              <div class="game-details" style="font-size: 0.95rem; color: #a0aec0;">NBA &#8226; SAS vs. SAC<br>Fri, Mar 7, 10:00 PM EST</div>
            </td>
            <td class="px-4 py-3 text-center">
      <div class="grid grid-cols-1 items-center gap-y-1 justify-items-center">
        <div class="pick-line-box" title="Under 24.5 (1.07x)">
          <span style="display: inline-block;">Under 24.5</span><span class="multiplier"> (1.07x)</span>
        </div>
        <p style="color: #ffffff; font-size: 1.25rem; text-align: center; margin: 0; font-weight: 500;" class="text-[1.25rem] text-white">
          Pts + Reb + Ast
        </p>
      </div>
        </td>
            <td class="px-4 py-3 high-ev">
      <div class="text-center w-full">
          <div class="ev-value font-bold mb-1 text-center" style="font-size: 1.5rem; color: #ffffff;">1.90%</div>
          <div class="flex justify-center gap-[2px]" aria-label="Expected value progress bar" role="progressbar" aria-valuenow="1.9" aria-valuemin="0" aria-valuemax="10">
              <div class="progress-segment" style="background-color: #ff3131; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #ff3131; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div>
          </div>
      </div>
      </td>
            <td class="px-4 py-3 odds-row"><div class="odds-div"><div class="border-[#292C33] odds-box-bet"><div class="odds-div"><strong>-105</strong></div></div></div></td>
            <td class="px-4 py-3"><div class="td-container flex items-center justify-center"><div class="mr-1">24.5</div><div class="odds-box" style="background-color:#ec3538; min-width: 100px; max-width: 110px;"><strong>-120</strong>&nbsp;/&nbsp;<strong>-108</strong></div></div></td><td class="px-4 py-3"></td><td class="px-4 py-3"></td><td class="px-4 py-3"><div class="td-container flex items-center justify-center"><div class="mr-1">24.5</div><div class="odds-box" style="background-color:#37cd3f; min-width: 100px; max-width: 110px;"><strong>−120</strong>&nbsp;/&nbsp;<strong>−110</strong></div></div></td><td class="px-4 py-3"><div class="td-container flex items-center justify-center"><div class="mr-1">24.5</div><div class="odds-box" style="background-color:#0c3533; min-width: 100px; max-width: 110px;"><strong>-117</strong>&nbsp;/&nbsp;<strong>-117</strong></div></div></td><td class="px-4 py-3"><div class="td-container flex items-center justify-center"><div class="mr-1">24.5</div><div class="odds-box" style="background-color:#ee4117; min-width: 100px; max-width: 110px;"><strong>-126</strong>&nbsp;/&nbsp;<strong>-105</strong></div></div></td>
        </tr>
        
        <tr class="hover:bg-[#2d3748] transition-colors duration-150" style="background-color: #1a1e25; font-size: 1.4rem;">
            <td class="px-4 py-3 text-center"><img src="https://github.com/tvnner2/sportsbook-images/blob/main/assets/Underdog.png?raw=true" alt="Underdog" class="site-image" style="width: auto; height: 36px; border-radius: 6px;"></td>
            <td class="px-4 py-3 text-center">
              <div class="player-name" style="font-size: 1.5rem; font-weight: 600;">Jaylin Williams</div>
              <div class="game-details" style="font-size: 0.95rem; color: #a0aec0;">NBA &#8226; POR vs. OKC<br>Fri, Mar 7, 8:00 PM EST</div>
            </td>
            <td class="px-4 py-3 text-center">
      <div class="grid grid-cols-1 items-center gap-y-1 justify-items-center">
        <div class="pick-line-box" title="Under 1.5 (1.24x)">
          <span style="display: inline-block;">Under 1.5</span><span class="multiplier"> (1.24x)</span>
        </div>
        <p style="color: #ffffff; font-size: 1.25rem; text-align: center; margin: 0; font-weight: 500;" class="text-[1.25rem] text-white">
          3-PT Made
        </p>
      </div>
        </td>
            <td class="px-4 py-3 high-ev">
      <div class="text-center w-full">
          <div class="ev-value font-bold mb-1 text-center" style="font-size: 1.5rem; color: #ffffff;">1.80%</div>
          <div class="flex justify-center gap-[2px]" aria-label="Expected value progress bar" role="progressbar" aria-valuenow="1.8" aria-valuemin="0" aria-valuemax="10">
              <div class="progress-segment" style="background-color: #ff3131; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div>
          </div>
      </div>
      </td>
            <td class="px-4 py-3 odds-row"><div class="odds-div"><div class="border-[#292C33] odds-box-bet"><div class="odds-div"><strong>+127</strong></div></div></div></td>
            <td class="px-4 py-3"><div class="td-container flex items-center justify-center"><div class="mr-1">1.5</div><div class="odds-box" style="background-color:#ec3538; min-width: 100px; max-width: 110px;"><strong>+118</strong>&nbsp;/&nbsp;<strong>-154</strong></div></div></td><td class="px-4 py-3"></td><td class="px-4 py-3"></td><td class="px-4 py-3"></td><td class="px-4 py-3"></td><td class="px-4 py-3"><div class="td-container flex items-center justify-center"><div class="mr-1">1.5</div><div class="odds-box" style="background-color:#ee4117; min-width: 100px; max-width: 110px;"><strong>+102</strong>&nbsp;/&nbsp;<strong>-135</strong></div></div></td>
        </tr>
        
        <tr class="hover:bg-[#2d3748] transition-colors duration-150" style="background-color: #13161c; font-size: 1.4rem;">
            <td class="px-4 py-3 text-center"><img src="https://github.com/tvnner2/sportsbook-images/blob/main/assets/Underdog.png?raw=true" alt="Underdog" class="site-image" style="width: auto; height: 36px; border-radius: 6px;"></td>
            <td class="px-4 py-3 text-center">
              <div class="player-name" style="font-size: 1.5rem; font-weight: 600;">Naji Marshall</div>
              <div class="game-details" style="font-size: 0.95rem; color: #a0aec0;">NBA &#8226; MEM vs. DAL<br>Fri, Mar 7, 7:30 PM EST</div>
            </td>
            <td class="px-4 py-3 text-center">
      <div class="grid grid-cols-1 items-center gap-y-1 justify-items-center">
        <div class="pick-line-box" title="Under 1.5 (1.17x)">
          <span style="display: inline-block;">Under 1.5</span><span class="multiplier"> (1.17x)</span>
        </div>
        <p style="color: #ffffff; font-size: 1.25rem; text-align: center; margin: 0; font-weight: 500;" class="text-[1.25rem] text-white">
          3-PT Made
        </p>
      </div>
        </td>
            <td class="px-4 py-3 high-ev">
      <div class="text-center w-full">
          <div class="ev-value font-bold mb-1 text-center" style="font-size: 1.5rem; color: #ffffff;">1.70%</div>
          <div class="flex justify-center gap-[2px]" aria-label="Expected value progress bar" role="progressbar" aria-valuenow="1.7" aria-valuemin="0" aria-valuemax="10">
              <div class="progress-segment" style="background-color: #ff3131; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div>
          </div>
      </div>
      </td>
            <td class="px-4 py-3 odds-row"><div class="odds-div"><div class="border-[#292C33] odds-box-bet"><div class="odds-div"><strong>+114</strong></div></div></div></td>
            <td class="px-4 py-3"><div class="td-container flex items-center justify-center"><div class="mr-1">1.5</div><div class="odds-box" style="background-color:#ec3538; min-width: 100px; max-width: 110px;"><strong>+103</strong>&nbsp;/&nbsp;<strong>-133</strong></div></div></td><td class="px-4 py-3"></td><td class="px-4 py-3"><div class="td-container flex items-center justify-center"><div class="mr-1">1.5</div><div class="odds-box" style="background-color:#0178ff; min-width: 100px; max-width: 110px;"><strong>-102</strong>&nbsp;/&nbsp;<strong>-130</strong></div></div></td><td class="px-4 py-3"><div class="td-container flex items-center justify-center"><div class="mr-1">1.5</div><div class="odds-box" style="background-color:#37cd3f; min-width: 100px; max-width: 110px;"><strong>+100</strong>&nbsp;/&nbsp;<strong>−130</strong></div></div></td><td class="px-4 py-3"><div class="td-container flex items-center justify-center"><div class="mr-1">1.5</div><div class="odds-box" style="background-color:#0c3533; min-width: 100px; max-width: 110px;"><strong>-106</strong>&nbsp;/&nbsp;<strong>-129</strong></div></div></td><td class="px-4 py-3"><div class="td-container flex items-center justify-center"><div class="mr-1">1.5</div><div class="odds-box" style="background-color:#ee4117; min-width: 100px; max-width: 110px;"><strong>-102</strong>&nbsp;/&nbsp;<strong>-130</strong></div></div></td>
        </tr>
        
        <tr class="hover:bg-[#2d3748] transition-colors duration-150" style="background-color: #1a1e25; font-size: 1.4rem;">
            <td class="px-4 py-3 text-center"><img src="https://github.com/tvnner2/sportsbook-images/blob/main/assets/Underdog.png?raw=true" alt="Underdog" class="site-image" style="width: auto; height: 36px; border-radius: 6px;"></td>
            <td class="px-4 py-3 text-center">
              <div class="player-name" style="font-size: 1.5rem; font-weight: 600;">Jake LaRavia</div>
              <div class="game-details" style="font-size: 0.95rem; color: #a0aec0;">NBA &#8226; SAS vs. SAC<br>Fri, Mar 7, 10:00 PM EST</div>
            </td>
            <td class="px-4 py-3 text-center">
      <div class="grid grid-cols-1 items-center gap-y-1 justify-items-center">
        <div class="pick-line-box" title="Under 5.5 (0.93x)">
          <span style="display: inline-block;">Under 5.5</span><span class="multiplier"> (0.93x)</span>
        </div>
        <p style="color: #ffffff; font-size: 1.25rem; text-align: center; margin: 0; font-weight: 500;" class="text-[1.25rem] text-white">
          Rebounds
        </p>
      </div>
        </td>
            <td class="px-4 py-3 high-ev">
      <div class="text-center w-full">
          <div class="ev-value font-bold mb-1 text-center" style="font-size: 1.5rem; color: #ffffff;">1.70%</div>
          <div class="flex justify-center gap-[2px]" aria-label="Expected value progress bar" role="progressbar" aria-valuenow="1.7" aria-valuemin="0" aria-valuemax="10">
              <div class="progress-segment" style="background-color: #ff3131; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div>
          </div>
      </div>
      </td>
            <td class="px-4 py-3 odds-row"><div class="odds-div"><div class="border-[#292C33] odds-box-bet"><div class="odds-div"><strong>-143</strong></div></div></div></td>
            <td class="px-4 py-3"><div class="td-container flex items-center justify-center"><div class="mr-1">5.5</div><div class="odds-box" style="background-color:#ec3538; min-width: 100px; max-width: 110px;"><strong>-175</strong>&nbsp;/&nbsp;<strong>+134</strong></div></div></td><td class="px-4 py-3"></td><td class="px-4 py-3"></td><td class="px-4 py-3"></td><td class="px-4 py-3"><div class="td-container flex items-center justify-center"><div class="mr-1">5.5</div><div class="odds-box" style="background-color:#0c3533; min-width: 100px; max-width: 110px;"><strong>-160</strong>&nbsp;/&nbsp;<strong>+116</strong></div></div></td><td class="px-4 py-3"><div class="td-container flex items-center justify-center"><div class="mr-1">5.5</div><div class="odds-box" style="background-color:#ee4117; min-width: 100px; max-width: 110px;"><strong>-155</strong>&nbsp;/&nbsp;<strong>+116</strong></div></div></td>
        </tr>
        
        <tr class="hover:bg-[#2d3748] transition-colors duration-150" style="background-color: #13161c; font-size: 1.4rem;">
            <td class="px-4 py-3 text-center"><img src="https://github.com/tvnner2/sportsbook-images/blob/main/assets/Underdog.png?raw=true" alt="Underdog" class="site-image" style="width: auto; height: 36px; border-radius: 6px;"></td>
            <td class="px-4 py-3 text-center">
              <div class="player-name" style="font-size: 1.5rem; font-weight: 600;">Darius Garland</div>
              <div class="game-details" style="font-size: 0.95rem; color: #a0aec0;">NBA &#8226; CLE vs. CHA<br>Fri, Mar 7, 7:00 PM EST</div>
            </td>
            <td class="px-4 py-3 text-center">
      <div class="grid grid-cols-1 items-center gap-y-1 justify-items-center">
        <div class="pick-line-box" title="Under 2.5 (1.11x)">
          <span style="display: inline-block;">Under 2.5</span><span class="multiplier"> (1.11x)</span>
        </div>
        <p style="color: #ffffff; font-size: 1.25rem; text-align: center; margin: 0; font-weight: 500;" class="text-[1.25rem] text-white">
          Rebounds
        </p>
      </div>
        </td>
            <td class="px-4 py-3 high-ev">
      <div class="text-center w-full">
          <div class="ev-value font-bold mb-1 text-center" style="font-size: 1.5rem; color: #ffffff;">1.30%</div>
          <div class="flex justify-center gap-[2px]" aria-label="Expected value progress bar" role="progressbar" aria-valuenow="1.3" aria-valuemin="0" aria-valuemax="10">
              <div class="progress-segment" style="background-color: #ff3131; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div>
          </div>
      </div>
      </td>
            <td class="px-4 py-3 odds-row"><div class="odds-div"><div class="border-[#292C33] odds-box-bet"><div class="odds-div"><strong>+104</strong></div></div></div></td>
            <td class="px-4 py-3"><div class="td-container flex items-center justify-center"><div class="mr-1">2.5</div><div class="odds-box" style="background-color:#ec3538; min-width: 100px; max-width: 110px;"><strong>-110</strong>&nbsp;/&nbsp;<strong>-118</strong></div></div></td><td class="px-4 py-3"></td><td class="px-4 py-3"><div class="td-container flex items-center justify-center"><div class="mr-1">2.5</div><div class="odds-box" style="background-color:#0178ff; min-width: 100px; max-width: 110px;"><strong>-110</strong>&nbsp;/&nbsp;<strong>-120</strong></div></div></td><td class="px-4 py-3"><div class="td-container flex items-center justify-center"><div class="mr-1">2.5</div><div class="odds-box" style="background-color:#37cd3f; min-width: 100px; max-width: 110px;"><strong>−110</strong>&nbsp;/&nbsp;<strong>−120</strong></div></div></td><td class="px-4 py-3"><div class="td-container flex items-center justify-center"><div class="mr-1">2.5</div><div class="odds-box" style="background-color:#0c3533; min-width: 100px; max-width: 110px;"><strong>-113</strong>&nbsp;/&nbsp;<strong>-121</strong></div></div></td><td class="px-4 py-3"></td>
        </tr>
        
        <tr class="hover:bg-[#2d3748] transition-colors duration-150" style="background-color: #1a1e25; font-size: 1.4rem;">
            <td class="px-4 py-3 text-center"><img src="https://github.com/tvnner2/sportsbook-images/blob/main/assets/Underdog.png?raw=true" alt="Underdog" class="site-image" style="width: auto; height: 36px; border-radius: 6px;"></td>
            <td class="px-4 py-3 text-center">
              <div class="player-name" style="font-size: 1.5rem; font-weight: 600;">Anfernee Simons</div>
              <div class="game-details" style="font-size: 0.95rem; color: #a0aec0;">NBA &#8226; POR vs. OKC<br>Fri, Mar 7, 8:00 PM EST</div>
            </td>
            <td class="px-4 py-3 text-center">
      <div class="grid grid-cols-1 items-center gap-y-1 justify-items-center">
        <div class="pick-line-box" title="Under 5.5 (0.94x)">
          <span style="display: inline-block;">Under 5.5</span><span class="multiplier"> (0.94x)</span>
        </div>
        <p style="color: #ffffff; font-size: 1.25rem; text-align: center; margin: 0; font-weight: 500;" class="text-[1.25rem] text-white">
          Assists
        </p>
      </div>
        </td>
            <td class="px-4 py-3 high-ev">
      <div class="text-center w-full">
          <div class="ev-value font-bold mb-1 text-center" style="font-size: 1.5rem; color: #ffffff;">1.30%</div>
          <div class="flex justify-center gap-[2px]" aria-label="Expected value progress bar" role="progressbar" aria-valuenow="1.3" aria-valuemin="0" aria-valuemax="10">
              <div class="progress-segment" style="background-color: #ff3131; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div>
          </div>
      </div>
      </td>
            <td class="px-4 py-3 odds-row"><div class="odds-div"><div class="border-[#292C33] odds-box-bet"><div class="odds-div"><strong>-138</strong></div></div></div></td>
            <td class="px-4 py-3"><div class="td-container flex items-center justify-center"><div class="mr-1">5.5</div><div class="odds-box" style="background-color:#ec3538; min-width: 100px; max-width: 110px;"><strong>-167</strong>&nbsp;/&nbsp;<strong>+128</strong></div></div></td><td class="px-4 py-3"></td><td class="px-4 py-3"><div class="td-container flex items-center justify-center"><div class="mr-1">5.5</div><div class="odds-box" style="background-color:#0178ff; min-width: 100px; max-width: 110px;"><strong>-144</strong>&nbsp;/&nbsp;<strong>+108</strong></div></div></td><td class="px-4 py-3"></td><td class="px-4 py-3"><div class="td-container flex items-center justify-center"><div class="mr-1">5.5</div><div class="odds-box" style="background-color:#0c3533; min-width: 100px; max-width: 110px;"><strong>-154</strong>&nbsp;/&nbsp;<strong>+112</strong></div></div></td><td class="px-4 py-3"><div class="td-container flex items-center justify-center"><div class="mr-1">5.5</div><div class="odds-box" style="background-color:#ee4117; min-width: 100px; max-width: 110px;"><strong>-167</strong>&nbsp;/&nbsp;<strong>+125</strong></div></div></td>
        </tr>
        
        <tr class="hover:bg-[#2d3748] transition-colors duration-150" style="background-color: #13161c; font-size: 1.4rem;">
            <td class="px-4 py-3 text-center"><img src="https://github.com/tvnner2/sportsbook-images/blob/main/assets/Underdog.png?raw=true" alt="Underdog" class="site-image" style="width: auto; height: 36px; border-radius: 6px;"></td>
            <td class="px-4 py-3 text-center">
              <div class="player-name" style="font-size: 1.5rem; font-weight: 600;">Max Strus</div>
              <div class="game-details" style="font-size: 0.95rem; color: #a0aec0;">NBA &#8226; CLE vs. CHA<br>Fri, Mar 7, 7:00 PM EST</div>
            </td>
            <td class="px-4 py-3 text-center">
      <div class="grid grid-cols-1 items-center gap-y-1 justify-items-center">
        <div class="pick-line-box" title="Over 2.5 (0.93x)">
          <span style="display: inline-block;">Over 2.5</span><span class="multiplier"> (0.93x)</span>
        </div>
        <p style="color: #ffffff; font-size: 1.25rem; text-align: center; margin: 0; font-weight: 500;" class="text-[1.25rem] text-white">
          Assists
        </p>
      </div>
        </td>
            <td class="px-4 py-3 high-ev">
      <div class="text-center w-full">
          <div class="ev-value font-bold mb-1 text-center" style="font-size: 1.5rem; color: #ffffff;">0.70%</div>
          <div class="flex justify-center gap-[2px]" aria-label="Expected value progress bar" role="progressbar" aria-valuenow="0.7" aria-valuemin="0" aria-valuemax="10">
              <div class="progress-segment" style="background-color: #ff3131; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div>
          </div>
      </div>
      </td>
            <td class="px-4 py-3 odds-row"><div class="odds-div"><div class="border-[#292C33] odds-box-bet"><div class="odds-div"><strong>-139</strong></div></div></div></td>
            <td class="px-4 py-3"><div class="td-container flex items-center justify-center"><div class="mr-1">2.5</div><div class="odds-box" style="background-color:#ec3538; min-width: 100px; max-width: 110px;"><strong>-156</strong>&nbsp;/&nbsp;<strong>+121</strong></div></div></td><td class="px-4 py-3"></td><td class="px-4 py-3"><div class="td-container flex items-center justify-center"><div class="mr-1">2.5</div><div class="odds-box" style="background-color:#0178ff; min-width: 100px; max-width: 110px;"><strong>-158</strong>&nbsp;/&nbsp;<strong>+118</strong></div></div></td><td class="px-4 py-3"><div class="td-container flex items-center justify-center"><div class="mr-1">2.5</div><div class="odds-box" style="background-color:#37cd3f; min-width: 100px; max-width: 110px;"><strong>−160</strong>&nbsp;/&nbsp;<strong>+124</strong></div></div></td><td class="px-4 py-3"><div class="td-container flex items-center justify-center"><div class="mr-1">2.5</div><div class="odds-box" style="background-color:#0c3533; min-width: 100px; max-width: 110px;"><strong>-160</strong>&nbsp;/&nbsp;<strong>+116</strong></div></div></td><td class="px-4 py-3"></td>
        </tr>
        
        <tr class="hover:bg-[#2d3748] transition-colors duration-150" style="background-color: #1a1e25; font-size: 1.4rem;">
            <td class="px-4 py-3 text-center"><img src="https://github.com/tvnner2/sportsbook-images/blob/main/assets/Underdog.png?raw=true" alt="Underdog" class="site-image" style="width: auto; height: 36px; border-radius: 6px;"></td>
            <td class="px-4 py-3 text-center">
              <div class="player-name" style="font-size: 1.5rem; font-weight: 600;">Donte DiVincenzo</div>
              <div class="game-details" style="font-size: 0.95rem; color: #a0aec0;">NBA &#8226; MIN vs. MIA<br>Fri, Mar 7, 8:00 PM EST</div>
            </td>
            <td class="px-4 py-3 text-center">
      <div class="grid grid-cols-1 items-center gap-y-1 justify-items-center">
        <div class="pick-line-box" title="Under 7.5 (1.07x)">
          <span style="display: inline-block;">Under 7.5</span><span class="multiplier"> (1.07x)</span>
        </div>
        <p style="color: #ffffff; font-size: 1.25rem; text-align: center; margin: 0; font-weight: 500;" class="text-[1.25rem] text-white">
          Reb + Ast
        </p>
      </div>
        </td>
            <td class="px-4 py-3 high-ev">
      <div class="text-center w-full">
          <div class="ev-value font-bold mb-1 text-center" style="font-size: 1.5rem; color: #ffffff;">0.50%</div>
          <div class="flex justify-center gap-[2px]" aria-label="Expected value progress bar" role="progressbar" aria-valuenow="0.5" aria-valuemin="0" aria-valuemax="10">
              <div class="progress-segment" style="background-color: #ff3131; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div><div class="progress-segment" style="background-color: #212b38; height: 5px; width: 14px; border-radius: 2px;"></div>
          </div>
      </div>
      </td>
            <td class="px-4 py-3 odds-row"><div class="odds-div"><div class="border-[#292C33] odds-box-bet"><div class="odds-div"><strong>-102</strong></div></div></div></td>
            <td class="px-4 py-3"></td><td class="px-4 py-3"></td><td class="px-4 py-3"><div class="td-container flex items-center justify-center"><div class="mr-1">7.5</div><div class="odds-box" style="background-color:#0178ff; min-width: 100px; max-width: 110px;"><strong>-118</strong>&nbsp;/&nbsp;<strong>-112</strong></div></div></td><td class="px-4 py-3"><div class="td-container flex items-center justify-center"><div class="mr-1">7.5</div><div class="odds-box" style="background-color:#37cd3f; min-width: 100px; max-width: 110px;"><strong>−115</strong>&nbsp;/&nbsp;<strong>−115</strong></div></div></td><td class="px-4 py-3"><div class="td-container flex items-center justify-center"><div class="mr-1">7.5</div><div class="odds-box" style="background-color:#0c3533; min-width: 100px; max-width: 110px;"><strong>-121</strong>&nbsp;/&nbsp;<strong>-113</strong></div></div></td><td class="px-4 py-3"></td>
        </tr>
        
                </tbody>
            </table>
            <div class="pagination-container">
                <button id="prevPage" class="pagination-button" >Previous</button>
                <div class="pagination-info">Page 2 of 3</div>
                <button id="nextPage" class="pagination-button" >Next</button>
            </div>
            <script>
                document.getElementById('prevPage').addEventListener('click', function() {
                    if (2 > 1) {
                        window.location.href = 'page-1.html';
                    }
                });
                
                document.getElementById('nextPage').addEventListener('click', function() {
                    if (2 < 3) {
                        window.location.href = 'page-3.html';
                    }
                });
            </script>
            </div>
    </div>
</body>

    </html>
    