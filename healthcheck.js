#!/usr/bin/env node

/**
 * Health check script for the DFS Feed application
 * This script can be used by Docker or monitoring systems to check application health
 */

const puppeteer = require('puppeteer');

async function healthCheck() {
    let browser = null;
    try {
        console.log('Starting health check...');
        
        // Test basic Puppeteer functionality
        const launchOptions = {
            headless: 'new',
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--single-process',
                '--disable-gpu',
                '--disable-crash-reporter',
                '--disable-breakpad'
            ]
        };

        if (process.env.PUPPETEER_EXECUTABLE_PATH) {
            launchOptions.executablePath = process.env.PUPPETEER_EXECUTABLE_PATH;
        }

        browser = await puppeteer.launch(launchOptions);
        const page = await browser.newPage();
        
        // Test basic page functionality
        await page.setContent('<html><body><h1>Health Check</h1></body></html>');
        await page.setViewport({ width: 800, height: 600 });
        
        // Take a small screenshot to test the full pipeline
        await page.screenshot({ type: 'png' });
        
        await page.close();
        await browser.close();
        
        console.log('Health check passed ✓');
        process.exit(0);
        
    } catch (error) {
        console.error('Health check failed ✗');
        console.error('Error:', error.message);
        
        if (browser) {
            try {
                await browser.close();
            } catch (closeError) {
                console.error('Error closing browser during health check:', closeError.message);
            }
        }
        
        process.exit(1);
    }
}

// Run health check if this script is executed directly
if (require.main === module) {
    healthCheck();
}

module.exports = healthCheck;
