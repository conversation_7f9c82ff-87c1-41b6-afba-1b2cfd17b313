/**
 * @file memoryManager.js
 * @description
 * Manages memory usage by monitoring the system's total RAM consumption and optionally
 * limiting concurrent Puppeteer browser instances. When memory usage exceeds a threshold,
 * it triggers garbage collection (if available) and signals that browsers should be cleaned.
 *
 * @notes
 * - Default memory usage threshold is 80%
 * - We also provide a concurrency limit to prevent too many simultaneous browser instances
 * - If concurrency is reached, new screenshot requests will wait in a queue
 *
 * @dependencies
 * - os: for totalmem/freemem
 * - global.gc if run with --expose-gc
 *
 * @assumptions
 * - The system or environment can run global.gc() (if the flag is used)
 * - The screenshot system calls trackBrowser(...) and safelyCloseBrowser(...) as we open/close them
 * - The concurrency approach helps limit overall memory usage from Puppeteer
 *
 * @limitations
 * - If other processes use memory, the threshold might be reached quickly
 * - We do not forcibly close browser instances in the middle of screenshot captures
 *
 * @example
 * memoryManager.startMonitoring();
 * ...
 * memoryManager.trackBrowser(browser);
 * ...
 * await memoryManager.safelyCloseBrowser(browser);
 */

const os = require('os');

class MemoryManager {
  /**
   * @param {number} thresholdPercent - If usedPercent of total RAM > thresholdPercent, attempt cleanup
   * @param {number} checkIntervalMs - How often we check memory usage
   * @param {number} maxBrowserConcurrency - The maximum number of Puppeteer instances allowed at once
   */
  constructor(thresholdPercent = 80, checkIntervalMs = 60000, maxBrowserConcurrency = 2) {
    this.thresholdPercent = thresholdPercent; 
    this.checkIntervalMs = checkIntervalMs;
    this.maxBrowserConcurrency = maxBrowserConcurrency; 

    this.intervalId = null;
    this.isMonitoring = false;

    // Track active browser instances
    this.browserInstances = new Set();

    // If concurrency is reached, we store pending requests in a queue
    this.browserLaunchQueue = [];
    this.pendingBrowserCleanup = false;
  }

  /**
   * Start memory monitoring on an interval. If usage is above threshold, attempt GC.
   */
  startMonitoring() {
    if (this.isMonitoring) return;
    
    console.log(`Starting memory monitoring (threshold: ${this.thresholdPercent}% of total RAM)`);
    this.isMonitoring = true;
    
    // Immediately check memory usage
    this.checkMemoryUsage();
    
    // Set up interval checks
    this.intervalId = setInterval(() => {
      this.checkMemoryUsage();
    }, this.checkIntervalMs);
    
    // Ensure the interval doesn't block process exit
    this.intervalId.unref();
  }

  /**
   * Stop the memory monitoring interval
   */
  stopMonitoring() {
    if (!this.isMonitoring) return;
    
    console.log('Stopping memory monitoring');
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    this.isMonitoring = false;
  }

  /**
   * Launch or attach a new browser instance with concurrency checks.
   * Instead of launching directly, usage should call this method to ensure concurrency is enforced.
   *
   * @param {Function} launchFn - A function returning a Promise that resolves to a Puppeteer Browser
   * @returns {Promise<any>} - Resolves to the Puppeteer Browser instance
   */
  async launchBrowserWithConcurrency(launchFn) {
    return new Promise((resolve, reject) => {
      // If we can launch immediately, do so. Otherwise, enqueue the request.
      if (this.browserInstances.size < this.maxBrowserConcurrency) {
        this._launchBrowser(launchFn, resolve, reject);
      } else {
        // Enqueue the request
        this.browserLaunchQueue.push({ launchFn, resolve, reject });
        console.log(`Browser concurrency limit reached. Queued requests: ${this.browserLaunchQueue.length}`);
      }
    });
  }

  /**
   * Internal helper to actually launch a browser, track it, and resolve the promise
   * @param {Function} launchFn
   * @param {Function} resolve
   * @param {Function} reject
   */
  async _launchBrowser(launchFn, resolve, reject) {
    try {
      const browser = await launchFn();
      this.trackBrowser(browser);
      resolve(browser);
    } catch (error) {
      console.error(`Error launching browser: ${error.message}`);
      reject(error);
    }
  }

  /**
   * Call this method once you're finished with the browser instance
   * so we can close it and free resources. Also processes the queue.
   * @param {any} browser - Puppeteer Browser instance
   */
  async safelyCloseBrowser(browser) {
    if (!browser) return false;
    
    try {
      await browser.close();
      this.untrackBrowser(browser);
      // After closing, we can see if there's a queue waiting
      this._processLaunchQueue();
      return true;
    } catch (error) {
      console.error(`Error safely closing browser: ${error.message}`);
      this.untrackBrowser(browser);
      this._processLaunchQueue();
      return false;
    }
  }

  /**
   * Processes queued browser launch requests if concurrency is available
   * Called automatically whenever we close a browser
   */
  _processLaunchQueue() {
    while (
      this.browserInstances.size < this.maxBrowserConcurrency &&
      this.browserLaunchQueue.length > 0
    ) {
      const { launchFn, resolve, reject } = this.browserLaunchQueue.shift();
      console.log(`Dequeuing a browser launch request. Queue length: ${this.browserLaunchQueue.length}`);
      this._launchBrowser(launchFn, resolve, reject);
    }
  }

  /**
   * Track a new browser instance in the set
   * @param {any} browser
   */
  trackBrowser(browser) {
    if (!browser) return;
    this.browserInstances.add(browser);
    console.log(`Tracking new browser instance (total: ${this.browserInstances.size})`);
  }

  /**
   * Untrack the browser instance after it's closed
   * @param {any} browser
   */
  untrackBrowser(browser) {
    if (!browser) return;
    this.browserInstances.delete(browser);
    console.log(`Browser instance closed (remaining: ${this.browserInstances.size})`);
  }

  /**
   * Check current memory usage. If usage exceeds threshold, attempt GC.
   */
  checkMemoryUsage() {
    const memoryUsage = process.memoryUsage();
    const totalRAM = os.totalmem();
    const freeRAM = os.freemem();
    const usedRAM = totalRAM - freeRAM;
    const usedPercent = (usedRAM / totalRAM) * 100;
    
    // Compute some process-level usage stats
    const rssGB = memoryUsage.rss / 1024 / 1024 / 1024;
    const heapTotalGB = memoryUsage.heapTotal / 1024 / 1024 / 1024;
    const heapUsedGB = memoryUsage.heapUsed / 1024 / 1024 / 1024;
    const externalGB = memoryUsage.external / 1024 / 1024 / 1024;
    
    console.log(`Memory usage: ${usedPercent.toFixed(2)}% of total RAM (${(usedRAM / 1024 / 1024 / 1024).toFixed(2)} GB used of ${(totalRAM / 1024 / 1024 / 1024).toFixed(2)} GB total)`);
    console.log(`Process memory: RSS: ${rssGB.toFixed(2)} GB, Heap Total: ${heapTotalGB.toFixed(2)} GB, Heap Used: ${heapUsedGB.toFixed(2)} GB, External: ${externalGB.toFixed(2)} GB`);
    
    // If memory usage exceeds threshold or we are near 85% of heap, try to free memory
    if (usedPercent > this.thresholdPercent || heapUsedGB > (heapTotalGB * 0.85)) {
      console.log('Memory usage exceeds threshold, attempting to free memory...');
      this.attemptMemoryCleanup();
    }
  }

  /**
   * Attempt to run garbage collection if available and set the flag to indicate
   * that browser cleanup may be needed soon.
   */
  async attemptMemoryCleanup() {
    if (global.gc) {
      console.log('Running global garbage collection');
      global.gc();
    } else {
      console.log('Garbage collection not exposed. To enable, run with --expose-gc flag');
    }
    
    // Log memory after GC
    const heapUsedAfterGC = process.memoryUsage().heapUsed / 1024 / 1024 / 1024;
    console.log(`Memory after cleanup attempt: Heap Used: ${heapUsedAfterGC.toFixed(2)} GB`);
    
    // Signal that we might want to do extra cleanup
    this.pendingBrowserCleanup = true;
  }

  /**
   * Indicates if we should do extra cleanup of idle browsers or skip some tasks
   * based on memory conditions
   * @returns {boolean}
   */
  shouldCleanupBrowsers() {
    return this.pendingBrowserCleanup;
  }

  /**
   * Reset the memory pressure flag once we've done any needed cleanup
   */
  resetCleanupFlag() {
    this.pendingBrowserCleanup = false;
  }

  /**
   * Return the current count of tracked browser instances
   * @returns {number}
   */
  getBrowserCount() {
    return this.browserInstances.size;
  }
}

// Export a single instance for the entire application
const memoryManager = new MemoryManager(
  80,  // thresholdPercent
  60000, // checkIntervalMs (1 minute)
  2 // maxBrowserConcurrency (adjust to taste)
);

module.exports = memoryManager;