/**
 * @file processMonitor.js
 * @description
 * Monitors system resources and provides mechanisms to restart the application
 * when resource usage becomes problematic. This helps prevent the Chrome spawn
 * issues that occur after days of running.
 */

const { spawn, exec } = require('child_process');
const os = require('os');

class ProcessMonitor {
    constructor() {
        this.startTime = Date.now();
        this.screenshotCount = 0;
        this.errorCount = 0;
        this.lastRestartTime = Date.now();
        this.isMonitoring = false;
        this.monitorInterval = null;
        
        // Thresholds for automatic restart
        this.MAX_UPTIME_HOURS = 24; // Restart after 24 hours
        this.MAX_ERRORS_PER_HOUR = 10; // Restart if too many errors
        this.MAX_MEMORY_USAGE_PERCENT = 95; // Restart if memory usage too high (adjusted for dev environment)
    }

    startMonitoring() {
        if (this.isMonitoring) return;
        
        console.log('Starting process monitoring...');
        this.isMonitoring = true;
        
        // Check every 5 minutes
        this.monitorInterval = setInterval(() => {
            this.checkSystemHealth();
        }, 5 * 60 * 1000);
        
        // Initial check
        this.checkSystemHealth();
    }

    stopMonitoring() {
        if (this.monitorInterval) {
            clearInterval(this.monitorInterval);
            this.monitorInterval = null;
        }
        this.isMonitoring = false;
        console.log('Process monitoring stopped.');
    }

    incrementScreenshotCount() {
        this.screenshotCount++;
    }

    incrementErrorCount() {
        this.errorCount++;
    }

    async checkSystemHealth() {
        const uptimeHours = (Date.now() - this.startTime) / (1000 * 60 * 60);
        const memoryUsage = this.getMemoryUsagePercent();
        const errorRate = this.getErrorRate();

        console.log(`System Health Check - Uptime: ${uptimeHours.toFixed(1)}h, Memory: ${memoryUsage.toFixed(1)}%, Errors/hr: ${errorRate.toFixed(1)}, Screenshots: ${this.screenshotCount}`);

        // Check if we need to restart
        let shouldRestart = false;
        let reason = '';

        if (uptimeHours > this.MAX_UPTIME_HOURS) {
            shouldRestart = true;
            reason = `Uptime exceeded ${this.MAX_UPTIME_HOURS} hours`;
        } else if (false && memoryUsage > 98) { // Temporarily disabled for testing
            shouldRestart = true;
            reason = `Memory usage exceeded 98% (critical level)`;
        } else if (errorRate > this.MAX_ERRORS_PER_HOUR) {
            shouldRestart = true;
            reason = `Error rate exceeded ${this.MAX_ERRORS_PER_HOUR} errors per hour`;
        }

        if (shouldRestart) {
            console.log(`🚨 RESTART TRIGGERED: ${reason}`);
            await this.performGracefulRestart(reason);
        }
    }

    getMemoryUsagePercent() {
        const totalMem = os.totalmem();
        const freeMem = os.freemem();
        const usedMem = totalMem - freeMem;
        return (usedMem / totalMem) * 100;
    }

    getErrorRate() {
        const uptimeHours = (Date.now() - this.startTime) / (1000 * 60 * 60);
        return uptimeHours > 0 ? this.errorCount / uptimeHours : 0;
    }

    async performGracefulRestart(reason) {
        console.log(`Performing graceful restart due to: ${reason}`);
        
        try {
            // Kill any lingering Chrome processes
            await this.killAllChromeProcesses();
            
            // Force garbage collection
            if (global.gc) {
                global.gc();
            }
            
            // Log restart event
            console.log('🔄 Application restarting to maintain stability...');
            
            // In a containerized environment, we exit and let the container orchestrator restart us
            process.exit(0);
            
        } catch (error) {
            console.error('Error during graceful restart:', error);
            // Force exit if graceful restart fails
            process.exit(1);
        }
    }

    async killAllChromeProcesses() {
        return new Promise((resolve) => {
            console.log('Killing all Chrome processes...');
            
            // Kill Chrome processes
            exec('pkill -f chrome', (error) => {
                if (error && error.code !== 1) { // code 1 means no processes found, which is OK
                    console.log('Note: pkill chrome returned:', error.code);
                }
                
                // Kill any remaining puppeteer processes
                exec('pkill -f puppeteer', (error2) => {
                    if (error2 && error2.code !== 1) {
                        console.log('Note: pkill puppeteer returned:', error2.code);
                    }
                    
                    // Small delay to let processes die
                    setTimeout(resolve, 2000);
                });
            });
        });
    }

    // Method to check if we should perform preventive cleanup
    shouldPerformPreventiveCleanup() {
        const uptimeHours = (Date.now() - this.startTime) / (1000 * 60 * 60);
        const memoryUsage = this.getMemoryUsagePercent();

        // Perform cleanup if we're getting close to thresholds (adjusted for dev environment)
        return uptimeHours > (this.MAX_UPTIME_HOURS * 0.8) ||
               memoryUsage > 97 || // Fixed threshold for high memory systems
               this.screenshotCount > 100;
    }

    async performPreventiveCleanup() {
        console.log('Performing preventive cleanup...');
        
        try {
            // Kill any orphaned Chrome processes
            await this.killAllChromeProcesses();
            
            // Force garbage collection
            if (global.gc) {
                global.gc();
            }
            
            // Reset counters
            this.screenshotCount = Math.floor(this.screenshotCount / 2);
            this.errorCount = Math.floor(this.errorCount / 2);
            
            console.log('Preventive cleanup completed.');
            
        } catch (error) {
            console.error('Error during preventive cleanup:', error);
        }
    }

    getStats() {
        const uptimeHours = (Date.now() - this.startTime) / (1000 * 60 * 60);
        return {
            uptimeHours: uptimeHours.toFixed(1),
            memoryUsagePercent: this.getMemoryUsagePercent().toFixed(1),
            screenshotCount: this.screenshotCount,
            errorCount: this.errorCount,
            errorRate: this.getErrorRate().toFixed(2)
        };
    }
}

// Export a singleton instance
const processMonitor = new ProcessMonitor();
module.exports = processMonitor;
