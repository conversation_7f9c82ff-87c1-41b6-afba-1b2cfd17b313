/**
 * @file betsTracker.js
 * @description
 * Manages tracking of "high EV bets" from different sources (e.g., PrizePicks, Underdog).
 * Compares newly fetched bets with previously stored bets to see if there are any changes,
 * such as newly added bets, removed bets, or changes in EV.
 *
 * @notes
 * - Rounds EV values to two decimals to avoid tiny float differences triggering false changes
 * - Uses a fallback key if projection_id is missing or empty
 * - Adjusts the difference threshold to 0.01% EV changes
 * - Maintains a "last sent time" map to avoid spamming updates
 * - JSON snapshots are no longer saved to the file system (per user request)
 *
 * @dependencies
 * - dayjs for date/time handling
 *
 * @assumptions
 * - Each bet in the database has a stable projection_id or the fallback key remains stable for identical bets
 * - The user is comfortable ignoring EV changes below 0.01%
 *
 * @limitations
 * - If aggregator feed data changes in fields we do not track, that won't be considered a "change"
 * - If fallback key fields change, that would cause the bet to be treated as new/removed
 *
 * @example
 * const hasChanges = betsTracker.updateHighEvBets('PrizePicks', newDocuments);
 * if (hasChanges) { ... }
 */

const { dayjs } = require('../config');

class BetsTracker {
  constructor() {
    /**
     * @type {Map<string, Map<string, any>>}
     * highEvBets is a Map of source -> Map of betKey -> bet data
     */
    this.highEvBets = new Map();

    /**
     * @type {Map<string, Date>}
     * lastSentTime tracks the last time we sent pages to Discord for each source
     */
    this.lastSentTime = new Map();
  }

  /**
   * Initializes the betsTracker by creating default empty maps for known sources.
   * Extend as needed if you have more DFS sites or providers to track.
   */
  init() {
    this.highEvBets.set('PrizePicks', new Map());
    this.highEvBets.set('Underdog', new Map());
    this.highEvBets.set('Betr', new Map());
    this.highEvBets.set('Dabble', new Map());
    this.highEvBets.set('Sleeper', new Map());
    // Add maps for the new sources
    this.highEvBets.set('VividPicks', new Map());
    this.highEvBets.set('Hotstreak', new Map());
    this.highEvBets.set('OwnersBox', new Map());
    console.log('BetsTracker initialized');
  }

  /**
   * Update the stored high EV bets for the given source and determine if changes exist.
   * @param {string} source - e.g. 'PrizePicks' or 'Underdog'
   * @param {Array<any>} currentHighEvBets - The newly fetched bets from the aggregator
   * @returns {boolean} - Whether there were changes (added bets, removed bets, or changed EV)
   */
  updateHighEvBets(source, currentHighEvBets) {
    const currentTime = dayjs().toDate();

    // If the map for this source doesn't exist yet, create it
    if (!this.highEvBets.has(source)) {
      this.highEvBets.set(source, new Map());
      console.log(`Created new map for ${source}`);
    }
    const sourceMap = this.highEvBets.get(source);

    // Filter out any bets that have already started
    currentHighEvBets = currentHighEvBets.filter(bet => {
      const startTime = dayjs(bet.start_time);
      return startTime.isAfter(currentTime);
    });

    console.log(`Processing ${currentHighEvBets.length} current bets for ${source}`);

    let hasChanges = false;
    const changedBets = [];
    const newBets = [];
    const removedBetIds = [];

    // Get all current IDs from the aggregator
    // We also create or use a fallback key if the aggregator's projection_id is missing/empty
    const currentIds = currentHighEvBets.map(bet => {
      // If aggregator has a stable projection_id, use it. Otherwise fallback:
      const fallbackKey = [
        bet.player_name || '',
        bet.og_stat_type || bet.stat_type || '',
        bet.source || '',
        bet.line || ''
      ].join('-').trim();

      const keyString = bet.projection_id
        ? bet.projection_id.toString()
        : fallbackKey;

      return keyString;
    });

    // The existing IDs are all the keys currently stored in the sourceMap
    const existingIds = Array.from(sourceMap.keys());

    // First run detection - if we have no bets in the tracker but have current bets, treat them all as new
    const isFirstRun = (sourceMap.size === 0 && currentHighEvBets.length > 0);
    if (isFirstRun) {
      console.log(`First run for ${source} - treating all ${currentHighEvBets.length} bets as new`);
      currentHighEvBets.forEach((bet) => {
        const fallbackKey = [
          bet.player_name || '',
          bet.og_stat_type || bet.stat_type || '',
          bet.source || '',
          bet.line || ''
        ].join('-').trim();

        const key = bet.projection_id
          ? bet.projection_id.toString()
          : fallbackKey;

        // We store the bet after rounding the EV so we compare consistently next time
        const roundEv = this.roundEv(bet.avg_ev_display);

        newBets.push({
          id: key,
          player: bet.player_name || 'Unknown',
          ev: roundEv
        });

        sourceMap.set(key, {
          ...bet,
          avg_ev_display: `${roundEv.toFixed(2)}%`, // override with the rounded string
          last_updated: new Date()
        });
      });
      hasChanges = true;

    } else {
      // For subsequent runs, do the standard new/removed/changed detection

      // Loop through current bets, see if they are new or if their EV changed
      currentHighEvBets.forEach((bet) => {
        const fallbackKey = [
          bet.player_name || '',
          bet.og_stat_type || bet.stat_type || '',
          bet.source || '',
          bet.line || ''
        ].join('-').trim();

        const key = bet.projection_id
          ? bet.projection_id.toString()
          : fallbackKey;

        if (sourceMap.has(key)) {
          // Already exists, check for EV changes
          const storedBet = sourceMap.get(key);

          const currentEv = this.roundEv(bet.avg_ev_display);
          const storedEv = this.roundEv(storedBet.avg_ev_display);

          // For EV changes above threshold
          const threshold = 0.01; // 0.01% difference to consider it a real change
          const difference = Math.abs(currentEv - storedEv);

          if (difference > threshold) {
            changedBets.push({
              id: key,
              player: bet.player_name || 'Unknown',
              oldEV: `${storedEv.toFixed(2)}%`,
              newEV: `${currentEv.toFixed(2)}%`,
              difference: `${difference.toFixed(2)}%`
            });
            hasChanges = true;
          }

        } else {
          // This is a new bet
          const roundEv = this.roundEv(bet.avg_ev_display);
          newBets.push({
            id: key,
            player: bet.player_name || 'Unknown',
            ev: roundEv
          });
          hasChanges = true;
        }
      });

      // Identify removed bets
      existingIds.forEach((id) => {
        if (!currentIds.includes(id)) {
          const bet = sourceMap.get(id);
          if (bet) {
            removedBetIds.push({
              id: id,
              player: bet.player_name || 'Unknown'
            });
          }
          hasChanges = true;
        }
      });
    }

    // Log the changed/new/removed bets
    if (changedBets.length > 0) {
      console.log(`Found ${changedBets.length} bets with EV changes for ${source}:`);
      changedBets.forEach(change => {
        console.log(`  - Player: ${change.player}, EV changed from ${change.oldEV} to ${change.newEV} (diff: ${change.difference})`);
      });
    }

    if (newBets.length > 0) {
      console.log(`Found ${newBets.length} new bets for ${source}:`);
      newBets.forEach(bet => {
        console.log(`  - New bet: ${bet.player}, EV: ${bet.ev.toFixed(2)}%`);
      });
    }

    if (removedBetIds.length > 0) {
      console.log(`Found ${removedBetIds.length} removed bets for ${source}:`);
      removedBetIds.forEach(bet => {
        console.log(`  - Removed bet: ${bet.player}, ID: ${bet.id}`);
        sourceMap.delete(bet.id);
      });
    }

    // Update the source map with the "final" data from currentHighEvBets
    // This ensures we store the newly updated EV, etc.
    currentHighEvBets.forEach((bet) => {
      const fallbackKey = [
        bet.player_name || '',
        bet.og_stat_type || bet.stat_type || '',
        bet.source || '',
        bet.line || ''
      ].join('-').trim();

      const key = bet.projection_id
        ? bet.projection_id.toString()
        : fallbackKey;

      const roundEv = this.roundEv(bet.avg_ev_display);

      sourceMap.set(key, {
        ...bet,
        avg_ev_display: `${roundEv.toFixed(2)}%`,
        last_updated: new Date()
      });
    });

    console.log(`${source} now has ${sourceMap.size} active bets in the tracker`);
    console.log(
      `updateHighEvBets summary for ${source}: changedBets=${changedBets.length}, newBets=${newBets.length}, removedBets=${removedBetIds.length}`
    );

    // Removed the JSON saving logic to stop creating files locally:
    // (Originally wrote snapshots to fs. Removed per user request)

    // If we found any changes, log them in detail
    if (hasChanges) {
      console.log(`Changes detected for ${source} - will trigger update!`);
    } else {
      console.log(`No changes detected for ${source} - no update needed.`);
    }

    return hasChanges;
  }

  /**
   * Get all active high EV bets for a given source
   * @param {string} source
   * @returns {Array<any>}
   */
  getActiveHighEvBets(source) {
    const sourceMap = this.highEvBets.get(source);
    return sourceMap ? Array.from(sourceMap.values()) : [];
  }

  /**
   * Decides whether we should send updates (HTML/screenshots) based on either forced send
   * or having passed a time threshold since last send
   * @param {string} source - e.g. 'PrizePicks' or 'Underdog'
   * @param {boolean} forceSend - if true, skip time checks
   * @returns {boolean}
   */
  shouldSendUpdates(source, forceSend = false) {
    if (forceSend) {
      console.log(`Force send enabled for ${source}`);
      return true;
    }

    // If we've never sent updates for this source, we should send them
    if (!this.lastSentTime.has(source)) {
      console.log(`First time sending updates for ${source}`);
      return true;
    }

    // Get the last time we sent updates for this source
    const lastSent = this.lastSentTime.get(source);

    // Check if it's been at least 30 minutes since we last sent updates
    const thirtyMinutesAgo = dayjs().subtract(30, 'minutes').toDate();
    const shouldSendDueToTime = lastSent < thirtyMinutesAgo;

    if (shouldSendDueToTime) {
      console.log(
        `Time interval reached for ${source} (last sent: ${dayjs(lastSent).format(
          'YYYY-MM-DD HH:mm:ss'
        )})`
      );
    } else {
      console.log(
        `Time interval not reached for ${source} (last sent: ${dayjs(lastSent).format(
          'YYYY-MM-DD HH:mm:ss'
        )})`
      );
    }

    return shouldSendDueToTime;
  }

  /**
   * Called once we've actually sent pages for a source, storing the time so we can skip
   * repeated sends until enough time passes or a new change occurs.
   * @param {string} source
   */
  markAsSent(source) {
    this.lastSentTime.set(source, new Date());
  }

  /**
   * Clears bets that are older than 6 hours from each source's map,
   * i.e. if their start time is in the past.
   */
  clearOldBets() {
    // Remove bets that have started more than 6 hours ago, or any that are in the past.
    for (const [source, sourceMap] of this.highEvBets.entries()) {
      for (const [id, bet] of sourceMap.entries()) {
        const startTime = dayjs(bet.start_time);
        // If the bet is in the past, remove it
        if (startTime.isBefore(dayjs())) {
          sourceMap.delete(id);
          console.log(`Removed past bet ${id} from ${source}`);
        }
      }
    }
  }

  /**
   * Helper to round a bet's EV string to two decimals as a float
   * If the input is "4.39%" or "5.272%", we convert to numeric 4.39 or 5.27
   * If parse fails, returns 0
   * @param {string} evDisplay
   * @returns {number}
   */
  roundEv(evDisplay) {
    if (typeof evDisplay !== 'string') return 0;
    const numericValue = parseFloat(evDisplay.replace(/[^\d.-]/g, '')) || 0;
    return parseFloat(numericValue.toFixed(2));
  }
}

module.exports = new BetsTracker();