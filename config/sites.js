const siteUrls = {
  BetMGM: {
    url: 'https://sports.betmgm.com/',
    emojiId: '1233099807871209555'
  },
  PropBuilder: {
    url: 'https://sports.betonline.ag/sportsbook/props',
    emojiId: '1233099809020186754'
  },
  Betr: {
    url: 'https://t.co/BHIrIkYDVW',
    emojiId: '1233099810035466280'
  },
  BetRivers: {
    url: 'https://nj.betrivers.com/?page=sportsbook&feed=featured#home',
    emojiId: '1233099811008286740'
  },
  BetSaracen: {
    url: 'https://sportsbook.betsaracen.com/en-us/home',
    emojiId: '1233099811972976751'
  },
  Boom: {
    url: 'https://t.co/5EeOpw8ixy',
    emojiId: '1233099813420007585'
  },
  Bovada: {
    url: 'https://www.bovada.lv/sports',
    emojiId: '1233099814665719878'
  },
  Dabble: {
    url: 'https://t.co/eW0nPssxqe',
    emojiId: '1233099909746397225'
  },
  ESPNBet: {
    url: 'https://espnbet.com/',
    emojiId: '1275127953843425433'
  },
  Fanatics: {
    url: 'https://sportsbook.fanatics.com/',
    emojiId: '1233099911130513428'
  },
  Fliff: {
    url: 'https://getfliff.com',
    emojiId: '1233099836178436150'
  },
  HardRock: {
    url: 'https://app.hardrock.bet/',
    emojiId: '1233099837675667542'
  },
  Hotstreak: {
    url: 'https://app.hotstreak.gg/',
    emojiId: '1274455539073552456'
  },
  Novig: {
    url: 'https://novig.onelink.me/JHQQ/l6ehyf6z',
    emojiId: '1289470051975565454'
  },
  JockMKT: {
    url: 'https://jockmkt.com/',
    emojiId: '1233099838762127441'
  },
  OaklawnSports: {
    url: 'https://oaklawnsports.com/sports.shtml#home',
    emojiId: '1233099841010270360'
  },
  ParlayPlay: {
    url: 'https://parlayplay.io/',
    emojiId: '1233099842251657326'
  },
  PrizePicks: {
    url: 'https://app.prizepicks.com/',
    emojiId: '1233099843979968544'
  },
  Sleeper: {
    url: 'https://sleeper.com/',
    emojiId: '1233099912057720903'
  },
  SportsBattle: {
    url: 'https://sportsquack.com/',
    emojiId: '1233099848824389662'
  },
  Underdog: {
    url: 'https://underdogfantasy.com/pick-em/higher-lower/all',
    emojiId: '1233099851575590933'
  },
  VividPicks: {
    url: 'https://www.vividpicks.com/',
    emojiId: '1233099913143779329'
  },
  Fanduel: {
    url: 'https://sportsbook.fanduel.com/',
    emojiId: '1233099823247392939'
  },
  DraftKings: {
    url: 'https://sportsbook.draftkings.com/',
    emojiId: '1180907091406356510'
  },
  Caesars: {
    url: 'https://sportsbook.caesars.com/',
    emojiId: '1233099815680872549'
  },
  BetOnline: {
    url: 'https://sports.betonline.ag/sportsbook/props',
    emojiId: '1233099809020186754'
  },
  Rebet: {
    url: 'https://rebet.app/',
    emojiId: '1234968833916141631'
  },
  Thrillzz: {
    url: 'https://app.thrillzz.com/#/sportsbook',
    emojiId: '1272641002313748573'
  },
  Pinnacle: {
    url: 'https://www.pinnacle.com/',
    emojiId: '1326598894725107803'
  },
  OwnersBox: {
    url: 'https://ownersbox.com/',
    emojiId: '1352670990064418826'
  }
};

const collectionImages = {
  BetMGM: 'https://raw.githubusercontent.com/tvnner2/sportsbook-images/main/assets/BetMGM.png',
  Betr: 'https://github.com/tvnner2/sportsbook-images/blob/main/assets/Betr.png?raw=true',
  BetRivers: 'https://github.com/tvnner2/sportsbook-images/blob/main/assets/BetRivers.png?raw=true',
  BetSaracen: 'https://github.com/tvnner2/sportsbook-images/blob/main/assets/BetSaracen.png?raw=true',
  Boom: 'https://github.com/tvnner2/sportsbook-images/blob/main/assets/Boom.png?raw=true',
  Bovada: 'https://raw.githubusercontent.com/datawisebets/logos/main/Bovada.png',
  Dabble: 'https://github.com/tvnner2/sportsbook-images/blob/main/assets/Dabble.png?raw=true',
  ESPNBet: 'https://raw.githubusercontent.com/datawisebets/logos/main/ESPNBet2.png',
  Fanatics: 'https://raw.githubusercontent.com/datawisebets/logos/main/Fanatics.png',
  Fliff: 'https://raw.githubusercontent.com/datawisebets/logos/main/Fliff.png',
  HardRock: 'https://raw.githubusercontent.com/datawisebets/logos/main/HardRock.png',
  Hotstreak: 'https://github.com/tvnner2/sportsbook-images/blob/main/assets/hotstreak.png?raw=true',
  JockMKT: 'https://github.com/tvnner2/sportsbook-images/blob/main/assets/JockMKT.png?raw=true',
  OaklawnSports: 'https://github.com/tvnner2/sportsbook-images/blob/main/assets/OaklawnSports.png?raw=true',
  ParlayPlay: 'https://github.com/tvnner2/sportsbook-images/blob/main/assets/ParlayPlay.png?raw=true',
  PrizePicks: 'https://raw.githubusercontent.com/datawisebets/logos/main/PrizePicks.png',
  PropBuilder: 'https://github.com/tvnner2/sportsbook-images/blob/main/assets/BetOnline.png?raw=true',
  Sleeper: 'https://github.com/tvnner2/sportsbook-images/blob/main/assets/Sleeper.png?raw=true',
  SportsBattle: 'https://github.com/tvnner2/sportsbook-images/blob/main/assets/SportsBattle.png?raw=true',
  Underdog: 'https://raw.githubusercontent.com/datawisebets/logos/main/Underdog.png',
  VividPicks: 'https://github.com/tvnner2/sportsbook-images/blob/main/assets/VividPicks.png?raw=true',
  Fanduel: 'https://raw.githubusercontent.com/datawisebets/logos/main/Fanduel.png',
  DraftKings: 'https://raw.githubusercontent.com/datawisebets/logos/main/DraftKings.png',
  Caesars: 'https://raw.githubusercontent.com/datawisebets/logos/main/Caesars.png',
  Kambi: 'https://github.com/tvnner2/sportsbook-images/blob/main/assets/Kambi.png?raw=true',
  BetOnline: 'https://github.com/tvnner2/sportsbook-images/blob/main/assets/BetOnline.png?raw=true',
  Circa: 'https://github.com/tvnner2/sportsbook-images/blob/main/assets/Circa.png?raw=true',
  Rebet: 'https://raw.githubusercontent.com/datawisebets/logos/main/rebet.png',
  Thrillzz: 'https://github.com/tvnner2/sportsbook-images/blob/main/assets/thrillzz.png?raw=true',
  Novig: 'https://raw.githubusercontent.com/datawisebets/logos/main/novig.png',
  Pinnacle: 'https://github.com/tvnner2/sportsbook-images/blob/main/assets/pinnacle.png?raw=true',
  OwnersBox: 'https://github.com/datawisebets/logos/blob/main/ownersbox.png?raw=true'
};

const providerColors = {
  Fanduel: "#0178ff",
  DraftKings: "#37cd3f",
  Caesars: "#0c3533",
  BetRivers: "#013a62",
  BetOnline: "#ec3538",
  PropBuilder: "#ec3538",
  Circa: "#000000",
  ESPNBet: "#04142b",
  Pinnacle: "#ee4117"
};

module.exports = {
  siteUrls,
  collectionImages,
  providerColors
};