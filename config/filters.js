/**
 * Configuration for bet filtering.
 * This file contains settings for filtering bets based on various criteria.
 */

// Default filter settings
const filterSettings = {
  // Minimum EV percentage to include a bet (e.g., 3.0 for 3.0%)
  minEV: 0.0,
  
  // Minimum win percentage to include a bet (e.g., 50 for 50%)
  minWinPercentage: 0,
  
  // List of stat types to include (empty array means include all)
  // Examples: ['points', 'rebounds', 'assists', 'three_pointers', 'steals', 'blocks']
  includedStatTypes: [],
  
  // List of stat types to exclude
  // Examples: ['turnovers', 'fouls']
  excludedStatTypes: [],
  
  // Whether to include alternative lines
  includeAltLines: true,
  
  // Minimum number of sharps required (unless <PERSON>due<PERSON> is one of them)
  minSharpsCount: 2,
  
  // Special sharps that can bypass the minimum count requirement
  specialSharps: ['Fanduel']
};

// Source-specific filter settings
const sourceFilterSettings = {
  // Underdog specific settings
  Underdog: {
    minEV: 3.0, // Minimum 3% EV for Underdog bets
  }
};

module.exports = {
  filterSettings,
  sourceFilterSettings
};
