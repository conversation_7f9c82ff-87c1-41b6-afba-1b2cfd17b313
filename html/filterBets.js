/**
 * Filter documents based on business criteria.
 * This module encapsulates the filtering logic for bets.
 */
const { filterSettings, sourceFilterSettings } = require('../config/filters');

/**
 * Safely extract a numeric line value from various possible shapes
 * @param {any} raw
 * @returns {number|NaN}
 */
function toNumber(raw) {
  if (raw == null) return NaN;
  if (typeof raw === 'number') return raw;
  // Mongo style { $numberDouble: "3.5" }
  if (typeof raw === 'object') {
    const inner = raw.$numberDouble ?? raw.$numberDecimal ?? raw.$numberInt ?? raw.value ?? undefined;
    return inner !== undefined ? parseFloat(inner) : NaN;
  }
  return parseFloat(raw);
}

/**
 * Filter bets based on business criteria.
 * @param {Array} documents - Array of documents to filter.
 * @param {Object} config - Configuration object with filtering criteria.
 * @returns {Array} - Filtered array of documents.
 */
function filterBets(documents, config = {}) {
  // Use the filter settings from config/filters.js as the default configuration
  const defaultConfig = filterSettings;

  // Merge default config with provided config
  const filterConfig = { ...defaultConfig, ...config };

  // Filter documents based on criteria
  return documents.filter((doc) => {
    // Apply source‑specific overrides if available
    let sourceConfig = filterConfig;
    if (doc.source && sourceFilterSettings[doc.source]) {
      sourceConfig = { ...filterConfig, ...sourceFilterSettings[doc.source] };
    }

    /* ------------------------- basic numeric/exclusion filters ------------------------- */
    const evPercentage = parseFloat((doc.avg_ev_display || '').replace('%', '')) || 0;
    const winPercentage = parseFloat((doc.datawise_percent || '').replace('%', '')) || 0;
    const statType = doc.og_stat_type || doc.stat_type;
    const league = doc.league || '';
    const isAltLine = doc.is_alt_line === true;

    if (evPercentage < sourceConfig.minEV) return false;
    if (winPercentage < sourceConfig.minWinPercentage) return false;
    if (sourceConfig.includedStatTypes.length && !sourceConfig.includedStatTypes.includes(statType)) return false;
    if (sourceConfig.excludedStatTypes.includes(statType)) return false;
    if (!sourceConfig.includeAltLines && isAltLine) return false;

    /* ------------------------- sharps count filters ------------------------- */
    const sharps = Array.isArray(doc.sharps) ? doc.sharps : [];
    const uniqueSharpSources = new Set(sharps.map((s) => s.source));
    const uniqueSharpsCount = uniqueSharpSources.size;
    const hasSpecialSharp = sharps.some((s) => sourceConfig.specialSharps.includes(s.source));

    const passesNormalSharpCount = uniqueSharpsCount >= sourceConfig.minSharpsCount;
    const passesDueToSpecialSharp = !passesNormalSharpCount && hasSpecialSharp;

    const isSoccerBet = league === 'SOCCER';
    const hasOnlyBetOnlineSharp = uniqueSharpsCount === 1 && uniqueSharpSources.has('BetOnline');
    const passesDueToSoccerRule = isSoccerBet && hasOnlyBetOnlineSharp;

    if (!passesNormalSharpCount && !passesDueToSpecialSharp && !passesDueToSoccerRule) return false;

    /* ------------------------- provider‑line match filter ------------------------- */
    // In the dataset the provider/collection line is "doc.line" (there isn’t a provider_line field)
    const providerLine = toNumber(doc.line);

    if (!isNaN(providerLine) && providerLine < 10) {
      const sharpLines = sharps.map((s) => toNumber(s.line)).filter((n) => !isNaN(n));
      const matchesSharp = sharpLines.some((sl) => sl === providerLine);
      if (!matchesSharp) return false;
    }

    return true; // passes all filters
  });
}

module.exports = filterBets;

