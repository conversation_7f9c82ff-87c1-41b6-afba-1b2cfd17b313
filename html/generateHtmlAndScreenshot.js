const puppeteer = require("puppeteer");
const memoryManager = require("../utils/memoryManager");

// Function to take a screenshot using Puppeteer
async function generateHtmlAndScreenshot(htmlContent) {
    let browser = null;
    try {
        browser = await puppeteer.launch({
            args: ['--no-sandbox', '--disable-setuid-sandbox', '--disable-dev-shm-usage']
        });
        
        // Track the browser instance
        memoryManager.trackBrowser(browser);
        
        const page = await browser.newPage();
        
        // Set content and optimize memory usage
        await page.setContent(htmlContent, { waitUntil: 'networkidle0' });
        
        // Adjust the viewport settings if necessary
        await page.setViewport({ width: 2100, height: 900 });
        
        // Screenshot the element with the 'data-container' class
        const element = await page.$(".data-container");
        if (!element) {
            throw new Error("Could not find element with class 'data-container'");
        }
        
        const screenshotBuffer = await element.screenshot();
        
        // Clean up resources
        await page.close();
        
        return screenshotBuffer;
    } catch (error) {
        console.error("Error generating screenshot:", error);
        throw error;
    } finally {
        // Always close the browser safely
        if (browser) {
            try {
                // Use the new safelyCloseBrowser method instead of directly closing
                await memoryManager.safelyCloseBrowser(browser);
            } catch (closeError) {
                console.error("Error safely closing browser:", closeError);
            }
        }
    }
}

module.exports = generateHtmlAndScreenshot;