const puppeteer = require("puppeteer");
const memoryManager = require("../utils/memoryManager");

// Function to take a screenshot using Puppeteer
async function generateHtmlAndScreenshot(htmlContent) {
    let browser = null;
    try {
        // Enhanced Chrome launch configuration for containerized environments
        const launchOptions = {
            headless: 'new', // Use new headless mode
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--single-process', // This is crucial for containerized environments
                '--disable-gpu',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding',
                '--disable-features=TranslateUI',
                '--disable-ipc-flooding-protection',
                '--disable-crash-reporter', // Disable crash reporter to avoid spawn issues
                '--disable-breakpad', // Disable breakpad crash reporting
                '--disable-component-update',
                '--disable-domain-reliability',
                '--disable-sync',
                '--disable-default-apps',
                '--disable-extensions',
                '--disable-plugins',
                '--disable-print-preview',
                '--disable-speech-api',
                '--disable-web-security',
                '--memory-pressure-off',
                '--max_old_space_size=4096'
            ]
        };

        // Use custom executable path if available (for Docker environments)
        if (process.env.PUPPETEER_EXECUTABLE_PATH) {
            launchOptions.executablePath = process.env.PUPPETEER_EXECUTABLE_PATH;
        }

        browser = await puppeteer.launch(launchOptions);

        // Track the browser instance
        memoryManager.trackBrowser(browser);

        const page = await browser.newPage();

        // Set additional page configurations for stability
        await page.setDefaultNavigationTimeout(30000);
        await page.setDefaultTimeout(30000);

        // Set content and optimize memory usage
        await page.setContent(htmlContent, {
            waitUntil: 'networkidle0',
            timeout: 30000
        });

        // Adjust the viewport settings if necessary
        await page.setViewport({ width: 2100, height: 900 });

        // Screenshot the element with the 'data-container' class
        const element = await page.$(".data-container");
        if (!element) {
            throw new Error("Could not find element with class 'data-container'");
        }

        const screenshotBuffer = await element.screenshot({
            type: 'png',
            encoding: 'binary'
        });

        // Clean up resources
        await page.close();

        return screenshotBuffer;
    } catch (error) {
        console.error("Error generating screenshot:", error);
        throw error;
    } finally {
        // Always close the browser safely
        if (browser) {
            try {
                // Use the new safelyCloseBrowser method instead of directly closing
                await memoryManager.safelyCloseBrowser(browser);
            } catch (closeError) {
                console.error("Error safely closing browser:", closeError);
            }
        }
    }
}

// Enhanced function with retry mechanism for production environments
async function generateHtmlAndScreenshotWithRetry(htmlContent, maxRetries = 3) {
    let lastError;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            console.log(`Screenshot generation attempt ${attempt}/${maxRetries}`);
            return await generateHtmlAndScreenshot(htmlContent);
        } catch (error) {
            lastError = error;
            console.error(`Screenshot generation attempt ${attempt} failed:`, error.message);

            if (attempt < maxRetries) {
                // Wait before retrying (exponential backoff)
                const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000);
                console.log(`Waiting ${delay}ms before retry...`);
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
    }

    throw new Error(`Screenshot generation failed after ${maxRetries} attempts. Last error: ${lastError.message}`);
}

module.exports = generateHtmlAndScreenshotWithRetry;