const puppeteer = require("puppeteer");
const memoryManager = require("../utils/memoryManager");
const { spawn } = require('child_process');

// Global counter to track screenshot attempts and force restarts
let screenshotCount = 0;
const MAX_SCREENSHOTS_BEFORE_RESTART = 50; // Restart after 50 screenshots to prevent accumulation

// Function to kill any lingering Chrome processes
async function killLingeringChromeProcesses() {
    return new Promise((resolve) => {
        // Kill any chrome processes that might be hanging around
        const killProcess = spawn('pkill', ['-f', 'chrome'], { stdio: 'ignore' });
        killProcess.on('close', () => {
            // Also try to kill any puppeteer-related processes
            const killPuppeteer = spawn('pkill', ['-f', 'puppeteer'], { stdio: 'ignore' });
            killPuppeteer.on('close', () => {
                resolve();
            });
            killPuppeteer.on('error', () => resolve()); // Ignore errors
        });
        killProcess.on('error', () => {
            // If pkill fails, just continue
            resolve();
        });
    });
}

// Function to take a screenshot using Puppeteer
async function generateHtmlAndScreenshot(htmlContent) {
    let browser = null;

    try {
        // Increment screenshot counter
        screenshotCount++;

        // Force cleanup every N screenshots to prevent resource accumulation
        if (screenshotCount % MAX_SCREENSHOTS_BEFORE_RESTART === 0) {
            console.log(`Reached ${screenshotCount} screenshots, performing aggressive cleanup...`);
            await killLingeringChromeProcesses();

            // Force garbage collection if available
            if (global.gc) {
                global.gc();
            }

            // Small delay to let cleanup complete
            await new Promise(resolve => setTimeout(resolve, 2000));
        }

        // Enhanced Chrome launch configuration for containerized environments
        const launchOptions = {
            headless: 'new', // Use new headless mode
            timeout: 30000, // 30 second timeout for launch
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--single-process', // This is crucial for containerized environments
                '--disable-gpu',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding',
                '--disable-features=TranslateUI,VizDisplayCompositor',
                '--disable-ipc-flooding-protection',
                '--disable-crash-reporter', // Disable crash reporter to avoid spawn issues
                '--disable-breakpad', // Disable breakpad crash reporting
                '--disable-component-update',
                '--disable-domain-reliability',
                '--disable-sync',
                '--disable-default-apps',
                '--disable-extensions',
                '--disable-plugins',
                '--disable-print-preview',
                '--disable-speech-api',
                '--disable-web-security',
                '--memory-pressure-off',
                '--max_old_space_size=4096',
                '--disable-background-networking',
                '--disable-client-side-phishing-detection',
                '--disable-default-apps',
                '--disable-hang-monitor',
                '--disable-popup-blocking',
                '--disable-prompt-on-repost',
                '--disable-sync',
                '--disable-translate',
                '--metrics-recording-only',
                '--no-default-browser-check',
                '--safebrowsing-disable-auto-update',
                '--enable-automation',
                '--password-store=basic',
                '--use-mock-keychain',
                '--disable-blink-features=AutomationControlled',
                '--force-color-profile=srgb',
                '--disable-features=TranslateUI,BlinkGenPropertyTrees',
                '--disable-logging',
                '--disable-login-animations',
                '--disable-notifications'
            ]
        };

        // Use custom executable path if available (for Docker environments)
        if (process.env.PUPPETEER_EXECUTABLE_PATH) {
            launchOptions.executablePath = process.env.PUPPETEER_EXECUTABLE_PATH;
        }

        browser = await puppeteer.launch(launchOptions);

        // Track the browser instance
        memoryManager.trackBrowser(browser);

        const page = await browser.newPage();

        // Set additional page configurations for stability
        page.setDefaultNavigationTimeout(30000);
        page.setDefaultTimeout(30000);

        // Disable images and CSS to speed up loading and reduce memory
        await page.setRequestInterception(true);
        page.on('request', (req) => {
            const resourceType = req.resourceType();
            if (resourceType === 'image' || resourceType === 'stylesheet' || resourceType === 'font') {
                req.abort();
            } else {
                req.continue();
            }
        });

        // Set content and optimize memory usage
        await page.setContent(htmlContent, {
            waitUntil: 'domcontentloaded', // Changed from networkidle0 to be faster
            timeout: 30000
        });

        // Adjust the viewport settings if necessary
        await page.setViewport({ width: 2100, height: 900 });

        // Screenshot the element with the 'data-container' class
        const element = await page.$(".data-container");
        if (!element) {
            throw new Error("Could not find element with class 'data-container'");
        }

        const screenshotBuffer = await element.screenshot({
            type: 'png',
            encoding: 'binary',
            optimizeForSpeed: true
        });

        // Aggressive cleanup
        page.removeAllListeners();
        await page.close();

        return screenshotBuffer;
    } catch (error) {
        console.error(`Error generating screenshot (attempt ${screenshotCount}):`, error);
        throw error;
    } finally {
        // Always close the browser safely with aggressive cleanup
        if (browser) {
            try {
                // Get all pages and close them individually
                const pages = await browser.pages();
                for (const page of pages) {
                    try {
                        await page.close();
                    } catch (pageCloseError) {
                        console.error("Error closing page:", pageCloseError.message);
                    }
                }

                // Use the new safelyCloseBrowser method
                await memoryManager.safelyCloseBrowser(browser);
            } catch (closeError) {
                console.error("Error safely closing browser:", closeError);

                // Force kill the browser process if normal close fails
                try {
                    const process = browser.process();
                    if (process) {
                        process.kill('SIGKILL');
                    }
                } catch (killError) {
                    console.error("Error force killing browser:", killError.message);
                }
            }
        }
    }
}

// Enhanced function with retry mechanism for production environments
async function generateHtmlAndScreenshotWithRetry(htmlContent, maxRetries = 3) {
    let lastError;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            console.log(`Screenshot generation attempt ${attempt}/${maxRetries}`);
            return await generateHtmlAndScreenshot(htmlContent);
        } catch (error) {
            lastError = error;
            console.error(`Screenshot generation attempt ${attempt} failed:`, error.message);

            if (attempt < maxRetries) {
                // Wait before retrying (exponential backoff)
                const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000);
                console.log(`Waiting ${delay}ms before retry...`);
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
    }

    throw new Error(`Screenshot generation failed after ${maxRetries} attempts. Last error: ${lastError.message}`);
}

module.exports = generateHtmlAndScreenshotWithRetry;