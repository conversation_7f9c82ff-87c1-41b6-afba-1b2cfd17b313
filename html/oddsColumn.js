function createOddsColumn(line, overOdds, underOdds, datawisePick, boxColor) {
  const lineContent = line ? `<div class="mr-1">${line}</div>` : "";
  let oddsContent = "";

  if (overOdds && underOdds) {
    // Bold the odds based on datawisePick and order them accordingly
    if (datawisePick === "Over") {
      oddsContent = `<div class="odds-box" style="background-color:${boxColor}; min-width: 100px; max-width: 110px;"><strong>${overOdds}</strong>&nbsp;/&nbsp;<strong>${underOdds}</strong></div>`;
    } else if (datawisePick === "Under") {
      oddsContent = `<div class="odds-box" style="background-color:${boxColor}; min-width: 100px; max-width: 110px;"><strong>${underOdds}</strong>&nbsp;/&nbsp;<strong>${overOdds}</strong></div>`;
    }
  }

  return lineContent || oddsContent
    ? `<td class="px-4 py-3"><div class="td-container flex items-center justify-center">${lineContent}${oddsContent}</div></td>`
    : `<td class="px-4 py-3"></td>`;
}

module.exports = createOddsColumn;