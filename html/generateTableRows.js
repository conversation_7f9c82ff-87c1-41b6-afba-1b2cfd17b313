const { dayjs } = require('../config');
const { collectionImages, providerColors } = require('../config/sites');
const createOddsColumn = require('./oddsColumn');
const { generateEvPctProgressBar, generateWinPctProgressBar } = require('./progressBars');

/**
 * Generates HTML table rows for the documents
 * @param {Array} documents - Array of document objects to generate rows for
 * @param {Set} activeProviders - Set of active providers
 * @param {string|null} devigSharp - Selected sharp source for devigging, if any
 * @returns {string} HTML string containing all table rows
 */
function generateTableRows(documents, activeProviders, devigSharp = null) {
  return documents
    .map((doc, index) => {
      const siteImageUrl = collectionImages[doc.source]; // Accessing the image URL using doc.collectionName
      const siteImageColumn = `<td class="px-4 py-3 text-center"><img src="${siteImageUrl}" alt="${doc.source}" class="site-image" style="width: auto; height: 36px; border-radius: 6px;"></td>`;

      // Always use doc.avg_ev_display for EV values
      let effectiveEv = parseFloat(doc.avg_ev_display.replace("%", "")) || 0;
      let effectiveWinPct = parseFloat(doc.datawise_percent.replace("%", "")) || 0;
      let effectiveDatawisePick = doc.datawise_pick;
      let effectiveQk = doc.qk_display;
      let effectiveLine = doc.line;  // Keep original line from source
      let sharpLine = doc.line;  // New variable for sharp's line
      let effectiveEvDisplay = doc.avg_ev_display;

      if (devigSharp && doc.sharps) {
        const selectedSharp = doc.sharps.find(sharp => sharp.source === devigSharp);
        if (selectedSharp) {
          // No longer changing effectiveEv or effectiveEvDisplay when a sharp is selected
          // Always using doc.avg_ev_display as requested

          // For win percentage, calculate it based on the model's prediction
          // If the pick is Over, use the model_over_percent, otherwise use model_under_percent
          effectiveWinPct = selectedSharp.datawise_pick === "Over" ? 
            parseFloat(doc.model_over_percent.replace("%", "")) :
            parseFloat(doc.model_under_percent.replace("%", ""));

          effectiveQk = selectedSharp.qk_display;
          effectiveDatawisePick = selectedSharp.datawise_pick || doc.datawise_pick;
          sharpLine = typeof selectedSharp.line === 'object' ? 
            selectedSharp.line?.$numberDouble : selectedSharp.line;
        }
      }

      // Generate the Odds Column Content based on effectiveDatawisePick
      const oddsContent = `<div class="border-[#292C33] odds-box-bet"><strong>${effectiveDatawisePick === "Over"
        ? doc.over_odds_american
        : doc.under_odds_american
        }</strong></div>`;
      const oddsColumn = `<td class="px-4 py-3 odds-row"><div class="odds-div">${oddsContent}</div></td>`;

      const oddsContentFair = `<div class="border-[#292C33] odds-box-bet"><div class="odds-div"><strong>${effectiveDatawisePick === "Over"
        ? doc.fairOdds.over
        : doc.fairOdds.under
        }</strong></div></div>`;
      const oddsColumnFair = `<td class="px-4 py-3 odds-row"><div class="odds-div">${oddsContentFair}</div></td>`;

      // Combined Stat Type, Over/Under, and Line with new styling
      // Calculate multiplier for Underdog bets
      let multiplier = '';
      if (doc.source === 'Underdog') {
        if (effectiveDatawisePick === 'Over') {
          if (doc.over_odds_decimal !== 1.86) {
            multiplier = '(' + (doc.over_odds_decimal / 1.86).toFixed(2) + 'x)';
          }
        } else if (effectiveDatawisePick === 'Under') {
          if (doc.under_odds_decimal !== 1.86) {
            multiplier = '(' + (doc.under_odds_decimal / 1.86).toFixed(2) + 'x)';
          }
        }
      }

      const statTypeWithLine = `
      <div class="grid grid-cols-1 items-center gap-y-1 justify-items-center">
        <div class="pick-line-box" title="${effectiveDatawisePick} ${effectiveLine}${multiplier}">
          <span style="display: inline-block;">${effectiveDatawisePick} ${effectiveLine}</span>${multiplier ? `<span class="multiplier" style="margin-left: 4px;">${multiplier}</span>` : ''}
        </div>
        <p style="color: #ffffff; font-size: 1.25rem; text-align: center; margin: 0; font-weight: 500;" class="text-[1.25rem] text-white">
          ${doc.og_stat_type || doc.stat_type}
        </p>
      </div>
        `;

      // Generate odds columns for active providers only
      const oddsColumns = Array.from(activeProviders)
        .map((provider) => {
          // Find the sharp object for the current provider
          const sharpForProvider = doc.sharps.find(sharp => sharp.source === provider);

          // Extract the providerLine, overOdds, and underOdds from the sharp object, if found
          const providerLine = sharpForProvider ? sharpForProvider.line : null;
          const overOdds = sharpForProvider ? sharpForProvider.over_odds_american : null;
          const underOdds = sharpForProvider ? sharpForProvider.under_odds_american : null;
          const boxColor = providerColors[provider] || "#2D3748";

          return createOddsColumn(
            providerLine,
            overOdds,
            underOdds,
            doc.datawise_pick,
            boxColor
          );
        })
        .join("");

      const qkContent = `<div class="border-[#292C33] odds-box-bet"><div class="odds-div"><strong>${effectiveQk}</strong></div></div>`;
      const qkColumn = `<td class="px-4 py-3 odds-row"><div class="odds-div">${qkContent}</div></td>`;

      const winPctProgressBar = generateWinPctProgressBar(
        effectiveWinPct,
        "Win percentage"
      );

      // Define a function to determine the color based on avg_ev
      function getEvColor(avgEv) {
        let avgEvColor;
        if (avgEv >= 0.05) { // greater than or equal to 5%
          avgEvColor = '#39ff14'; // GREEN
        } else if (avgEv > 0.03 && avgEv < 0.05) { // greater than 3% but less than 5%
          avgEvColor = '#faed27'; // YELLOW
        } else { // less than or equal to 3.0%
          avgEvColor = '#ff3131'; // RED
        }
        return avgEvColor;
      }

      // Add a CSS class based on EV value
      const evClass = effectiveEv >= 0.05 ? 'high-ev' : '';

      // Update the section where you generate the EV progress bar
      const evColor = getEvColor(effectiveEv);
      const evProgressBar = generateEvPctProgressBar(
        effectiveEv,
        evColor,
        "Expected value"
      );
      
      // Use only the progress bar for EV display which already includes the percentage
      const formattedEvDisplay = evProgressBar;

      let rawStartTime;
      if (doc.source === "Sleeper" && doc.sharps.length > 0) {
        rawStartTime = doc.sharps[0].start_time;
      } else {
        rawStartTime = doc.start_time;
      }
      const startTime = dayjs(new Date(`${rawStartTime}`));
      const prettyDate = startTime.tz("America/Toronto").format("ddd, MMM D, h:mm A EST");

      // Use alternating row colors based on index
      const rowBackgroundColor = index % 2 === 0 ? "#13161c" : "#1a1e25";
      
      return `
        <tr class="hover:bg-[#2d3748] transition-colors duration-150" style="background-color: ${rowBackgroundColor}; font-size: 1.4rem;">
            ${siteImageColumn}
            <td class="px-4 py-3 text-center">
              <div class="player-name" style="font-size: 1.5rem; font-weight: 600;">${doc.player_name}</div>
              <div class="game-details" style="font-size: 0.95rem; color: #a0aec0;">${doc.league} &#8226; ${doc.matchup}<br>${prettyDate}</div>
            </td>
            <td class="px-4 py-3 text-center">${statTypeWithLine}</td>
            <td class="px-4 py-3 ${evClass}">${formattedEvDisplay}</td>
            ${oddsColumnFair}
            ${oddsColumns}
        </tr>
        `;
    })
    .join("");
}

module.exports = generateTableRows;
