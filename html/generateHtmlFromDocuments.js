const { collectionImages } = require('../config/sites');
const filterActiveProviders = require('./filterActiveProviders');
const generateTableRows = require('./generateTableRows');
const generateHtmlTemplate = require('./generateHtmlTemplate');

/**
 * Generates HTML content for documents with pagination support
 * @param {Array} documents - Array of document objects to generate HTML for
 * @param {string|null} devigSharp - Selected sharp source for devigging, if any
 * @param {number} itemsPerPage - Number of items per page (default: 10)
 * @param {number} pageNumber - Page number to generate (default: 1)
 * @returns {Object} Object containing HTML content, total pages, and current page info
 */
function generateHtmlFromDocuments(documents, devigSharp = null, itemsPerPage = 10, pageNumber = 1) {
  // Sort documents by EV% in descending order
  documents.sort((a, b) => {
    const evA = parseFloat(a.avg_ev_display.replace("%", "")) || 0;
    const evB = parseFloat(b.avg_ev_display.replace("%", "")) || 0;
    return evB - evA; // For descending order
  });

  const activeProviders = filterActiveProviders(documents);

  // Generate table headers dynamically based on active providers
  const providerColumnsHeaders = Array.from(activeProviders)
    .map((provider) => {
      // Determine the appropriate image source
      // Use the Kambi image if the provider is BetRivers, otherwise use the provider's default image
      const imageSource = provider === 'BetRivers' ? collectionImages['Kambi'] : collectionImages[provider];

      return `
    <th style="padding: 1rem 1rem; font-size: 22px; font-weight: 700; color: #ffffff; letter-spacing: 0.05em; text-transform: uppercase; border-bottom: 2px solid #1e293b;">
      <div class="flex justify-center">
          <img class="block mx-auto rounded-md" src="${imageSource}" alt="${provider}" style="width: auto; height: 36px; border-radius: 6px;" />
      </div>
    </th>
  `;
    })
    .join("");

  // Calculate total number of pages
  const totalPages = Math.ceil(documents.length / itemsPerPage);
  
  // Ensure page number is valid
  pageNumber = Math.max(1, Math.min(pageNumber, totalPages));
  
  // Get documents for current page
  const startIndex = (pageNumber - 1) * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage, documents.length);
  const pageDocuments = documents.slice(startIndex, endIndex);
  
  // Generate table rows for current page
  const tableRows = generateTableRows(pageDocuments, activeProviders, devigSharp);
  
  // Generate HTML for current page
  const htmlContent = generateHtmlTemplate(providerColumnsHeaders, tableRows);
  
  return {
    html: htmlContent,
    totalPages: totalPages,
    currentPage: pageNumber,
    hasNextPage: pageNumber < totalPages,
    hasPrevPage: pageNumber > 1,
    totalItems: documents.length,
    pageItems: pageDocuments.length
  };
}

module.exports = generateHtmlFromDocuments;