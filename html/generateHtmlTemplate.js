/**
 * Generates the HTML template structure with CSS styles
 * @param {string} tableHeaders - HTML string containing table header columns
 * @param {string} tableRows - HTML string containing table rows
 * @returns {string} Complete HTML document as a string
 */
function generateHtmlTemplate(tableHeaders, tableRows) {
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>DataWise Optimizer</title>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
        <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
        <style>
            :root {
                --accent-color: #13161c;
                --base-font-size: 1.5rem; /* Further increased base font size for better readability */
                --primary-text: #ffffff; /* Primary text color for important data */
                --secondary-text: #a0aec0; /* Secondary text color for less important data */
                --highlight-color: #ffffff; /* Changed from green to white */
                --row-alt-color: #1a1e25; /* Alternating row color */
                --row-base-color: #13161c; /* Base row color */
                --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            }
            body {
                font-family: var(--font-family);
                font-size: var(--base-font-size);
                line-height: 1.6;
                background-color: var(--accent-color);
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
            }
            .site-image {
                width: 50px;
                height: 50px;
                border-radius: 50%;
                object-fit: cover;
                display: block;
                margin: 0 auto;
            }
            .pick-line-box {
              display: inline-block;
              background-color: #2d3748;
              border: 1px solid #4a5568;
              border-radius: 6px; /* Increased border radius */
              padding: 6px 10px; /* Increased padding */
              font-weight: 600; /* Made bolder for emphasis */
              min-width: 130px; /* Increased minimum width */
              color: white;
              font-size: 1.3rem; /* Further increased font size */
              box-shadow: 0 2px 4px rgba(0,0,0,0.2); /* Added subtle shadow */
              margin: 0 auto; /* Center the box */
              white-space: nowrap; /* Prevent line breaks */
              text-align: center; /* Center text */
          }
          /* Multiplier styling */
          .multiplier {
              display: inline-block;
              margin-left: 2px;
              color: white; /* Same color as main text */
              font-weight: 600; /* Match the font weight */
              font-size: 1.3rem; /* Match the updated font size */
          }
            .line {
                font-weight: normal;
                margin-left: 4px;
                margin-right: 4px;
            flex: 1;
            text-align: center;
            }
            .odds-box {
                display: inline-flex;
                justify-content: center;
                align-items: center;
                white-space: nowrap;
                background-color: #2d3748;
                --tw-border-opacity: 1;
                border-color: rgb(41 44 51 / var(--tw-border-opacity));
                border-radius: 6px; /* Increased border radius */
                padding: 6px 10px; /* Increased padding */
                margin-left: 2px;
                color: white;
                font-size: 1.2rem; /* Increased font size */
                font-weight: 600; /* Made text bolder */
                min-width: 95px; /* Increased width */
                max-width: 105px; /* Increased width */
                overflow: visible;
            }
            .odds-box-bet {
              display: flex; /* Adjusted to match flex display */
              justify-content: center;
              align-items: center;
              white-space: nowrap;
              background-color: #2d3748; /* Matches pick-line-box */
              border: 1px solid #4a5568; /* Matches pick-line-box */
              border-radius: 6px; /* Increased border radius */
              padding: 6px 10px; /* Increased padding */
              color: white; /* Matches pick-line-box */
              font-size: 1.3rem; /* Further increased font size */
              font-weight: 600; /* Made bolder for emphasis */
              min-width: 100px; /* Further increased for better readability */
              max-width: 120px; /* Further increased for better readability */
              overflow: visible; /* Specific to odds-box-bet */
              box-shadow: 0 2px 4px rgba(0,0,0,0.2); /* Added subtle shadow */
            }
            
            
            .custom-bg { background-color: var(--accent-color); }
            .custom-header { background-color: var(--accent-color); color: #f7fafc; }
            .custom-table-header { background-color: var(--accent-color); }
            .custom-table-header img {
                height: 32px;
                width: auto;
            }
            .custom-table-row { background-color: #2d3748; }
            .data-container {
                max-width: 100vw;
                margin: auto;
                box-shadow: 0 4px 6px rgba(0,0,0,0.3); /* Increased shadow depth */
                border-radius: 0.75rem; /* Increased border radius */
                overflow: visible;
            }
            .wide-column {
                width: 120px;
            }
            
            .narrow-column {
                width: 100px;
            }
            .progress-segment {
                height: 2px;
                width: 20px;
                clip-path: polygon(40% 0px, 100% 0px, 60% 100%, 0px 100%);
              }
            table {
                table-layout: fixed;
                min-width: 1100px;
                width: 100%;
                border-collapse: separate;
                border-spacing: 0 1px; /* Add slight spacing between rows */
            }
            .custom-table-row {
                height: 68px; /* Further increased row height */
            }
            
            .custom-table-row td, .custom-table-row th {
                font-size: 1.4rem; /* Further increased font size for better readability */
                padding: 10px; /* Added padding for better spacing */
            }
            /* Alternating row colors with increased contrast */
            tr:nth-child(odd) {
                background-color: var(--row-base-color);
            }
            tr:nth-child(even) {
                background-color: var(--row-alt-color);
            }
            /* Add hover effect for better visual feedback */
            tr:hover {
                background-color: #2d3748;
            }
            /* Add styling for important values */
            .ev-value, .win-value {
                font-weight: 700;
                font-size: 1.4rem; /* Further increased font size */
                color: var(--primary-text);
            }
            /* Style for high EV values - no longer using green text */
            .high-ev {
                color: var(--primary-text) !important;
                font-weight: 700; /* Using bold instead of color to emphasize */
            }
            /* Specifically target the player data cell for more width */
            th.player-data, td.player-data {
                width: 180px;
                text-align: center;
            }
        
            /* For columns that can afford to be narrower, adjust accordingly */
            th.odds-column, td.odds-column {
                width: 80px;
                text-align: center;
            }
            
            /* Ensure all table cells are centered */
            table th, table td {
                text-align: center;
            }
            .odds-div{
                display: flex;
                justify-content: center;
                align-items: center;
            }
            .thin{
                width: 100px;
            }
            .td-container {
                display: flex;
                justify-content: center;
                align-items: center;
                height: 100%; /* Ensure both elements fill the height of the td */
                gap: 2px; /* Reduced gap between elements */
            }
            .wide{
                width: 220px; /* Reduced from 290px */
            }
            .less-wide{
              width: 260px; /* Increased from 215px to show more of player names */
          }
            /* Player name styling */
            .player-name {
                font-family: var(--font-family);
                font-size: 1.4rem;
                font-weight: 700;
                color: #ffffff;
                margin-bottom: 4px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                max-width: 100%;
            }
            /* Game details styling */
            .game-details {
                font-family: var(--font-family);
                font-size: 0.85rem;
                color: #a0aec0;
                line-height: 1.3;
            }
            
        </style>
    </head>
    <body class="custom-bg text-gray-300 flex flex-col items-center justify-center">
<div class="data-container max-w-4xl w-full my-5">
    <div class="custom-header flex justify-center items-center px-6 py-6">
        <img src="https://github.com/tvnner2/sportsbook-images/blob/main/assets/Both_WhiteText_TransparentBackground.png?raw=true" alt="DataWise Optimizer Logo" style="height: 40px; width: auto;">
    </div>
        <table class="min-w-full leading-normal">
                <thead>
                <tr class="custom-table-header text-gray-400" style="border-bottom: 2px solid #2d3748;">
                <th class="thin text-center" style="padding: 1rem 1rem; font-size: 22px; font-weight: 700; color: #ffffff; letter-spacing: 0.05em; text-transform: uppercase; border-bottom: 2px solid #1e293b;">Site</th>
                <th class="less-wide text-center" style="padding: 1rem 1rem; font-size: 22px; font-weight: 700; color: #ffffff; letter-spacing: 0.05em; text-transform: uppercase; border-bottom: 2px solid #1e293b;">Player Info</th>
                <th class="wide text-center" style="padding: 1rem 1rem; font-size: 22px; font-weight: 700; color: #ffffff; letter-spacing: 0.05em; text-transform: uppercase; border-bottom: 2px solid #1e293b;">Bet Details</th>
                <th style="padding: 1rem 1rem; text-align: center; font-size: 22px; font-weight: 700; color: #ffffff; letter-spacing: 0.05em; text-transform: uppercase; border-bottom: 2px solid #1e293b;">EV %</th>
                <th style="padding: 1rem 1rem; text-align: center; font-size: 22px; font-weight: 700; color: #ffffff; letter-spacing: 0.05em; text-transform: uppercase; border-bottom: 2px solid #1e293b;">Fair Odds</th>
                        <!-- Dynamic provider columns -->
                        ${tableHeaders}
                    </tr>
                </thead>
                <tbody>
                    ${tableRows}
                </tbody>
            </table>
            </div>
    </div>
</body>

    </html>
    `;
}

module.exports = generateHtmlTemplate;