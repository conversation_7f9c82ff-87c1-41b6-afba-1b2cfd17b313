function filterActiveProviders(documents) {
  const activeProviders = new Set();

  // Define the list of valid providers for easier checks
  const validProviders = [
    "Fanduel",
    "DraftKings",
    "Caesars",
    "BetRivers",
    "BetOnline",
    "Circa",
    "ESPNBet",
    "Pinnacle"
  ];

  // Iterate over each document
  documents.forEach((doc) => {
    // Check each sharp in the sharps array
    doc.sharps.forEach((sharp) => {
      // If the source is one of the valid providers, add it to the set
      if (validProviders.includes(sharp.source)) {
        activeProviders.add(sharp.source);
      }
    });
  });

  return activeProviders;
}

module.exports = filterActiveProviders;