/**
 * Generates an EV percentage progress bar with colored segments
 * @param {number} evPercentage - The EV percentage value
 * @param {string} color - The color for filled segments
 * @param {string} label - Accessibility label for the progress bar
 * @returns {string} HTML string for the EV progress bar
 */
function generateEvPctProgressBar(evPercentage, color, label) {
  const totalSegments = 8; // Reduced from 12 to 8 segments

  // Adjust EV value to a scale from 0 to 10% for display
  // Any value < 0% is treated as 0%, and > 10% is treated as 10%
  let adjustedEvPercentage = Math.min(Math.max(evPercentage, 0), 10);

  // Calculate the number of filled segments based on the adjusted EV percentage
  // If evPercentage < 0, fill 1 segment; if evPercentage > 10, fill all segments
  let filledSegments = Math.round(
    (adjustedEvPercentage / 10) * totalSegments
  );

  // Ensure at least 1 segment is filled for any negative EV, and all for EV > 10%
  if (evPercentage <= 1) filledSegments = 1;
  if (evPercentage > 10) filledSegments = totalSegments;

  let segmentsHtml = "";

  // Define colors for the progress bar segments based on EV value
  let segmentColor;
  if (evPercentage >= 5) {
    segmentColor = "#39ff14"; // Keep green for the bar segments only
  } else if (evPercentage > 3 && evPercentage < 5) {
    segmentColor = "#faed27"; // Yellow for medium EV
  } else {
    segmentColor = "#ff3131"; // Red for low EV
  }

  for (let i = 0; i < totalSegments; i++) {
    const bgColor = i < filledSegments ? segmentColor : "#212b38"; // Using the segment color for filled segments
    segmentsHtml += `<div class="progress-segment" style="background-color: ${bgColor}; height: 5px; width: 14px; border-radius: 2px;"></div>`;
  }

  // Apply styling for EV values but without color differences
  const evClass = 'ev-value';

  return `
      <div class="text-center w-full">
          <div class="${evClass} font-bold mb-1 text-center" style="font-size: 1.5rem; color: #ffffff;">${evPercentage.toFixed(2)}%</div>
          <div class="flex justify-center gap-[2px]" aria-label="${label} progress bar" role="progressbar" aria-valuenow="${adjustedEvPercentage}" aria-valuemin="0" aria-valuemax="10">
              ${segmentsHtml}
          </div>
      </div>
      `;
}

/**
 * Generates a win percentage progress bar
 * @param {number} percentage - The win percentage value
 * @param {string} label - Accessibility label for the progress bar
 * @returns {string} HTML string for the win percentage progress bar
 */
function generateWinPctProgressBar(percentage, label) {
  const totalSegments = 20;
  const filledSegments = Math.round((percentage / 100) * totalSegments);
  let segmentsHtml = "";

  for (let i = 0; i < totalSegments; i++) {
    const bgColor = i < filledSegments ? "#FFFFFF" : "#2a3545";
    segmentsHtml += `<div class="progress-segment" style="background-color: ${bgColor}; height: 3px; width: 16px;"></div>`;
  }

  return `
      <div class="text-center w-full">
          <div class="win-value font-bold mb-1 text-center" style="font-size: 1.25rem">${percentage}%</div>
          <div class="flex justify-center gap-[2px]" aria-label="${label} progress bar" role="progressbar" aria-valuenow="${percentage}" aria-valuemin="0" aria-valuemax="100">
              ${segmentsHtml}
          </div>
      </div>
      `;
}

module.exports = {
  generateEvPctProgressBar,
  generateWinPctProgressBar
};
