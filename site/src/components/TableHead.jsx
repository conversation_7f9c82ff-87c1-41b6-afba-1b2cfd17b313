import React from 'react'

function TableHead() {
  return (
    <thead className=" border-b-2 border-b-white/10">
    <tr>
      <th scope="col" className="px-6 text-[16px] text-[#818181] py-4">
        Site
      </th>
      <th scope="col" className="px-6 text-[16px] text-[#818181] py-4">
        Player
      </th>
      <th scope="col" className="px-6 text-[16px] text-[#818181] py-4">
      Prop Bet
      </th>
      <th scope="col" className="px-6 text-center text-[16px] text-[#818181] py-4">
        Odds
      </th>
      <th scope="col" className="px-6 text-center text-[16px] text-[#818181] py-4">
      Win %
      </th>
      <th scope="col" className="px-6 text-[16px] text-[#818181] py-4">
      EV %
      </th>
      <th scope="col" className="px-6 py-4">
        <img className='block mx-auto' src="/fd.png"/>
      </th>
      <th scope="col" className="px-6 py-4">
        <img className='block mx-auto' src="/dk.png"/>
      </th>
      <th scope="col" className="px-6 py-4">
        <img className='block mx-auto' src="/trophy.png"/>
      </th>
      <th scope="col" className="px-6 py-4">
        <img className='block mx-auto' src="/kambi.png"/>
      </th>
      <th scope="col" className="px-6 py-4">
        <img className='block mx-auto' src="/b.png"/>
      </th>
      <th scope="col" className="px-6 py-4">
        <img className='block mx-auto w-[28px h-[28px]' src="/c.png"/>
      </th>
    </tr>
  </thead>
  )
}

export default TableHead