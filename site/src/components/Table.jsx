import React from 'react'
import TableHead from './TableHead';
import TableRow from './TableRow';
import Site from './Site'
import Player from './Player';
import PropBet from './PropBet'
import Odds from './Odds'
import Win from './Win'
import Ev from './Ev'
import FanDuel from './FanDuel';
import DraftKings from './DraftKings';
import Trophy from './Trophy';
import Kambi from './Kambi';
import BTr from './BTr';
import Circa from './Circa';
import Pinnacle from './Pinnacle';

function Table() {
    const totalItems = 20;
    const filledItemsCount = Math.floor((23 / 100) * totalItems);
    const items = Array.from({ length: totalItems }, (_, index) => (
      <div
        key={index}
        className={`w-[2px] h-[8px] ${index < filledItemsCount ? 'bg-white' : 'bg-[#3A4450]'} rotate-[5deg]`}
      />
    ));
  return (
    <table className="min-w-full text-left text-sm whitespace-nowrap">
    <thead className=" border-b-2 border-b-white/10">
    <tr>
      <th scope="col" className="px-6 text-[16px] text-[#818181] py-4">
        Site
      </th>
      <th scope="col" className="px-6 text-[16px] text-[#818181] py-4">
        Player
      </th>
      <th scope="col" className="px-6 text-[16px] text-[#818181] py-4">
      Prop Bet
      </th>
      <th scope="col" className="px-6 text-center text-[16px] text-[#818181] py-4">
        Odds
      </th>
      <th scope="col" className="px-6 text-center text-[16px] text-[#818181] py-4">
      Win %
      </th>
      <th scope="col" className="px-6 text-[16px] text-[#818181] py-4">
      EV %
      </th>
      <th scope="col" className="px-6 py-4">
        <img className='block mx-auto' src="/fd.png"/>
      </th>
      <th scope="col" className="px-6 py-4">
        <img className='block mx-auto' src="/dk.png"/>
      </th>
      <th scope="col" className="px-6 py-4">
        <img className='block mx-auto' src="/trophy.png"/>
      </th>
      <th scope="col" className="px-6 py-4">
        <img className='block mx-auto' src="/kambi.png"/>
      </th>
      <th scope="col" className="px-6 py-4">
        <img className='block mx-auto' src="/b.png"/>
      </th>
      <th scope="col" className="px-6 py-4">
        <img className='block mx-auto w-[28px h-[28px]' src="/c.png"/>
      </th>
      <th scope="col" className="px-6 py-4">
        <img className='block mx-auto' src="/pinnacle.png"/>
      </th>
    </tr>
  </thead>
    <tbody>
      <TableRow>
        <Site img={'b2'}/>
        <Player player={"Kevin Durant"} teamOne={"NBA"} teamTwo={"CLE"}/>
        <PropBet title={"Assist"} score={"4.5"}/>
        <Odds scoreOne={156} scoreTwo={120}/>
        <Win percent={23}/>
        <Ev percent={90}/>
        <FanDuel overall={3.5} scoreOne={114} scoreTwo={146}/>
        <DraftKings overall={3.5} scoreOne={114} scoreTwo={146}/>
        <Trophy overall={3.5} scoreOne={114} scoreTwo={146}/>
        <Kambi overall={3.5} scoreOne={114} scoreTwo={146}/>
        <BTr overall={3.5} scoreOne={114} scoreTwo={146}/>
        <Circa overall={3.5} scoreOne={114} scoreTwo={146}/>
        <Pinnacle overall={3.5} scoreOne={114} scoreTwo={146}/>
        </TableRow>

        <TableRow>
        <Site img={'b2'}/>
        <Player player={"Kevin Durant"} teamOne={"NBA"} teamTwo={"CLE"}/>
        <PropBet title={"Assist"} score={"4.5"}/>
        <Odds scoreOne={156} scoreTwo={120}/>
        <Win percent={23}/>
        <Ev percent={90}/>
        <FanDuel overall={3.5} scoreOne={114} scoreTwo={146}/>
        <DraftKings overall={3.5} scoreOne={114} scoreTwo={146}/>
        <Trophy overall={3.5} scoreOne={114} scoreTwo={146}/>
        <Kambi overall={3.5} scoreOne={114} scoreTwo={146}/>
        <BTr overall={3.5} scoreOne={114} scoreTwo={146}/>
        <Circa overall={3.5} scoreOne={114} scoreTwo={146}/>
        <Pinnacle overall={3.5} scoreOne={114} scoreTwo={146}/>
        </TableRow>
    </tbody>
  </table>
  )
}

export default Table