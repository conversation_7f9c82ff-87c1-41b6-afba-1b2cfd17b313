import React from 'react'

function PropBet({title, score}) {
  return (
    <td className="px-6 py-4">
    <div className='flex flex-row items-center gap-x-[14px]'>
    <p className='text-[14px] text-[#DBDBDB]'>{title}</p>
    <div className='bg-[#31353B] border border-[2px] w-[68px] h-[24px] flex flex-col items-start justify-center text-center border-[#292C33] rounded-[5px]'>
      <p className='text-white text-[10px] w-full font-bold'>Under {score}</p>
    </div>
    </div>
  </td>
  )
}

export default PropBet