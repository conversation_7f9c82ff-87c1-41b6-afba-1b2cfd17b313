import React from 'react'

function Pinnacle({ overall, scoreOne, scoreTwo }) {
  return (
    <td className="px-6 py-4">
      <div className="flex flex-row items-center gap-x-[14px]">
        <p className="text-[14px] text-[#DBDBDB]">{overall}</p>
        <div className="bg-[#FF8000] border border-[2px] w-[68px] h-[24px] flex flex-col items-start justify-center text-center border-[#FF8000] rounded-[5px]">
          <p className="text-white text-[10px] w-full font-bold">
            +{scoreOne}/ -{scoreTwo}
          </p>
        </div>
      </div>
    </td>
  );
}

export default Pinnacle; 