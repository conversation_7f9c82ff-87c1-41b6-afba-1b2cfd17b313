
import React from 'react'

function Win({percent}) {
    const totalItems = 20;
    const filledItemsCount = Math.floor((parseInt(percent) / 100) * totalItems);
    const items = Array.from({ length: totalItems }, (_, index) => (
      <div
        key={index}
        className={`w-[2px] h-[8px] ${index < filledItemsCount ? `bg-white` : 'bg-[#3A4450]'} rotate-[5deg]`}
      />
    ));
  return (
    <td className="px-6 py-4">
    <div className='flex flex-col items-center justify-center gap-y-[6px]'>
      <p className='text-[#DBDBDB] text-[14px]'>{percent}%</p>
      <div className='flex flex-row gap-x-[2px]'>
        {items}
      </div>
    </div>
  </td>
  )
}

export default Win