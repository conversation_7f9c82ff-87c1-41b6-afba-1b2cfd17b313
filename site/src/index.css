/**
 * @description
 * Global CSS file for the React front end.
 * This file enforces consistent styling across all components, 
 * including setting a universal font-family that matches your local Mac environment if possible.
 * 
 * Key features:
 * - @font-face for custom "San Francisco" or other Mac fonts (if licensed).
 * - Fallbacks to other system UI fonts on non-Mac systems.
 * - Tailwind base/components/utilities are imported at the bottom.
 * 
 * @notes
 * - If you do not have legal license to distribute SF Pro, consider an open-source alternative, 
 *   or keep these font files private.
 * - On Ubuntu servers, the Mac fonts won't exist by default, 
 *   so copying or installing them is necessary for identical rendering.
 * - The user must place the actual .ttf files in site/public/fonts/ or a similar folder.
 */

@font-face {
  font-family: 'SFProDisplay';
  src: url('/fonts/SF-Pro-Display-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  /* Adjust the path above if you place your font TTF in a different folder under public/. */
}

/* You can add more weights/styles if you have them, e.g.:
@font-face {
  font-family: 'SFProDisplay';
  src: url('/fonts/SF-Pro-Display-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
}
*/

body {
  margin: 0;
  /* 
     We try to use SFProDisplay or 'SF Pro' first, 
     and if not available, fallback to the original chain. 
  */
  font-family: SFProDisplay, 
               'SF Pro Text', 
               'SF Pro Display', 
               -apple-system, 
               BlinkMacSystemFont, 
               'Segoe UI', 
               'Roboto', 
               'Oxygen', 
               'Ubuntu', 
               'Cantarell', 
               'Fira Sans', 
               'Droid Sans', 
               'Helvetica Neue', 
               sans-serif;
  
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Code fonts remain the same. */
code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New', monospace;
}

/* Import Tailwind layers. */
/* stylelint-disable */
@tailwind base;
@tailwind components;
@tailwind utilities;
/* stylelint-enable */