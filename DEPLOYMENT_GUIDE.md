# DFS Feed Deployment Guide

## Puppeteer Chrome Launch Issues - ROBUSTLY FIXED

This guide addresses the Chrome launch issues you were experiencing in your Coolify deployment, specifically the problem where it works for days then fails.

### Problem Summary
The error `posix_spawn /root/.cache/puppeteer/chrome/linux-126.0.6478.126/chrome-linux64/chrome_crashpad_handler: Operation not permitted (1)` was occurring because:

1. **Resource Accumulation**: Chrome processes and memory accumulate over days of running
2. **File Descriptor Leaks**: Long-running containers develop resource leaks
3. **Memory Fragmentation**: Container memory becomes fragmented over time
4. **Process Zombie Issues**: Orphaned Chrome processes interfere with new launches

### Solutions Implemented

#### 1. **Aggressive Resource Management**
- **Periodic Process Cleanup**: Kills lingering Chrome processes every 50 screenshots
- **Automatic Restarts**: Application restarts itself after 24 hours or high resource usage
- **Memory Monitoring**: Tracks memory usage and triggers cleanup when needed
- **Process Monitoring**: Comprehensive system health monitoring

#### 2. **Enhanced Chrome Configuration**
- **Single Process Mode**: `--single-process` prevents subprocess spawn issues
- **Crash Reporter Disabled**: Eliminates the crashpad_handler spawn problem
- **Resource Optimization**: Disabled images/CSS loading for faster screenshots
- **Aggressive Cleanup**: Force-kills browser processes if normal close fails

#### 3. **Container Optimization**
- **Non-root User**: Security best practice with proper permissions
- **Garbage Collection**: Enabled with `--expose-gc` flag
- **Health Monitoring**: Built-in health checks every 30 seconds
- **Startup Script**: Enhanced startup with monitoring and auto-restart

#### 4. **Long-term Stability**
- **Preventive Cleanup**: Proactive resource cleanup before issues occur
- **Error Tracking**: Monitors error rates and triggers restarts if needed
- **System Stats**: Regular logging of system health metrics
- **Graceful Degradation**: Continues operation even if individual screenshots fail

### Files Modified/Created

1. **Enhanced Screenshot Generation** (`html/generateHtmlAndScreenshot.js`)
   - Aggressive Chrome launch configuration with 30+ optimization flags
   - Periodic process cleanup (every 50 screenshots)
   - Force-kill mechanisms for stuck processes
   - Request interception to disable images/CSS for speed

2. **Process Monitoring System** (`utils/processMonitor.js`) - **NEW**
   - Monitors uptime, memory usage, and error rates
   - Automatic restart triggers (24h uptime, 90% memory, high error rate)
   - Preventive cleanup mechanisms
   - System health reporting

3. **Enhanced Startup** (`start.sh`) - **NEW**
   - Container-aware startup script
   - Automatic restart logic with backoff
   - Memory monitoring and cleanup
   - Signal handling for graceful shutdowns

4. **Optimized Docker Configuration** (`Dockerfile`)
   - Non-root user with proper permissions
   - Enhanced Chrome installation
   - Garbage collection enabled
   - Health check integration

5. **Application Integration** (`index.js`)
   - Process monitor integration
   - Error tracking and reporting
   - Preventive cleanup triggers
   - Enhanced shutdown handling

6. **Supporting Files**
   - `.dockerignore` - Optimized builds
   - `healthcheck.js` - Health monitoring
   - `package.json` - Added health check script

### Deployment Steps

#### For Coolify Deployment:

1. **Rebuild your container** with the updated Dockerfile
2. **Set restart policy** in Coolify to "always" or "unless-stopped"
3. **Verify environment variables** are set correctly:
   ```
   PUPPETEER_EXECUTABLE_PATH=/usr/bin/google-chrome-stable
   CHROME_BIN=/usr/bin/google-chrome-stable
   PUPPETEER_DISABLE_HEADLESS_WARNING=true
   NODE_ENV=production
   ```

4. **Monitor the enhanced logging** - Look for these new log messages:
   - `📊 System Stats` - Every 10 minutes
   - `Screenshot generation attempt X/3` - Retry attempts
   - `Reached X screenshots, performing aggressive cleanup` - Preventive cleanup
   - `🚨 RESTART TRIGGERED` - Automatic restart events

5. **Health monitoring** - The container includes comprehensive health checks

#### Alternative: If Issues Persist

If you still encounter issues, you can try these additional steps:

1. **Add to your Coolify environment variables**:
   ```
   PUPPETEER_ARGS=--no-sandbox,--disable-setuid-sandbox,--disable-dev-shm-usage,--single-process
   ```

2. **Increase container memory** if needed (Chrome can be memory-intensive)

3. **Enable privileged mode** in Coolify (last resort, less secure)

### Testing the Fix

Run the health check manually:
```bash
node healthcheck.js
```

This will test the complete Puppeteer pipeline and confirm everything is working.

### Monitoring

The application now includes:
- Retry mechanism with exponential backoff
- Enhanced error logging
- Health check endpoint
- Memory management improvements

### Performance Notes

- The `--single-process` flag may slightly impact performance but is necessary for container stability
- Memory usage is optimized with proper cleanup and the existing memory manager
- Screenshots now have timeout protection to prevent hanging

### Support

If you continue to experience issues:
1. Check the container logs for the new retry messages
2. Verify the health check is passing
3. Ensure your Coolify deployment has sufficient memory allocated
4. Consider the alternative environment variables mentioned above
