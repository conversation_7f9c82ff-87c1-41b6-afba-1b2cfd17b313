# DFS Feed Deployment Guide

## Puppeteer Chrome Launch Issues - Fixed

This guide addresses the Chrome launch issues you were experiencing in your Coolify deployment.

### Problem Summary
The error `posix_spawn /root/.cache/puppeteer/chrome/linux-126.0.6478.126/chrome-linux64/chrome_crashpad_handler: Operation not permitted (1)` was occurring because:

1. <PERSON><PERSON>'s crash handler couldn't spawn in the containerized environment
2. Missing security flags for containerized Chrome execution
3. Running as root user (security issue)

### Solutions Implemented

#### 1. Enhanced Chrome Launch Configuration
- Added `--single-process` flag (crucial for containers)
- Disabled crash reporter and breakpad to prevent spawn issues
- Added comprehensive security and performance flags
- Implemented retry mechanism with exponential backoff

#### 2. Improved Docker Configuration
- Updated Chrome installation with proper GPG key handling
- Created non-root user (`pptruser`) for security
- Set proper file permissions
- Added health check functionality

#### 3. Error Handling & Resilience
- Implemented retry mechanism for screenshot generation
- Added proper timeout configurations
- Enhanced error logging and debugging

### Files Modified

1. **`html/generateHtmlAndScreenshot.js`**
   - Enhanced Chrome launch options
   - Added retry mechanism
   - Improved error handling

2. **`Dockerfile`**
   - Updated Chrome installation
   - Added non-root user
   - Improved security configuration
   - Added health check

3. **New Files Created**
   - `.dockerignore` - Optimized Docker builds
   - `healthcheck.js` - Application health monitoring
   - `DEPLOYMENT_GUIDE.md` - This guide

### Deployment Steps

#### For Coolify Deployment:

1. **Rebuild your container** with the updated Dockerfile
2. **Verify environment variables** are set correctly:
   ```
   PUPPETEER_EXECUTABLE_PATH=/usr/bin/google-chrome-stable
   CHROME_BIN=/usr/bin/google-chrome-stable
   PUPPETEER_DISABLE_HEADLESS_WARNING=true
   ```

3. **Monitor the health check** - The container now includes a health check that runs every 30 seconds

4. **Check logs** for the new retry mechanism messages

#### Alternative: If Issues Persist

If you still encounter issues, you can try these additional steps:

1. **Add to your Coolify environment variables**:
   ```
   PUPPETEER_ARGS=--no-sandbox,--disable-setuid-sandbox,--disable-dev-shm-usage,--single-process
   ```

2. **Increase container memory** if needed (Chrome can be memory-intensive)

3. **Enable privileged mode** in Coolify (last resort, less secure)

### Testing the Fix

Run the health check manually:
```bash
node healthcheck.js
```

This will test the complete Puppeteer pipeline and confirm everything is working.

### Monitoring

The application now includes:
- Retry mechanism with exponential backoff
- Enhanced error logging
- Health check endpoint
- Memory management improvements

### Performance Notes

- The `--single-process` flag may slightly impact performance but is necessary for container stability
- Memory usage is optimized with proper cleanup and the existing memory manager
- Screenshots now have timeout protection to prevent hanging

### Support

If you continue to experience issues:
1. Check the container logs for the new retry messages
2. Verify the health check is passing
3. Ensure your Coolify deployment has sufficient memory allocated
4. Consider the alternative environment variables mentioned above
